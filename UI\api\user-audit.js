/**
 * 用户审核相关API
 * 处理员工审核用户、获取待审核用户列表等功能
 */

import request from '../utils/request.js'

/**
 * 用户审核请求参数
 * @typedef {Object} VideoUserAuditDto
 * @property {number} status - 审核状态 (1:通过 2:拒绝)
 * @property {string} remark - 审核备注
 */

/**
 * 用户响应数据
 * @typedef {Object} VideoUserResponseDto
 * @property {number} id - 用户ID
 * @property {string} openId - 微信OpenID
 * @property {string} unionId - 微信UnionID
 * @property {string} nickname - 用户昵称
 * @property {string} avatar - 用户头像
 * @property {string} employeeId - 员工ID
 * @property {string} employeeName - 员工姓名
 * @property {string} lastLogin - 最后登录时间
 * @property {string} createTime - 创建时间
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 员工审核用户
 * @param {number} userId - 用户ID
 * @param {VideoUserAuditDto} data - 审核数据
 * @returns {Promise<ApiResult<boolean>>} 审核结果
 */
export function auditUser (userId, data) {
  return request.post(`/UserAudit/audit-user/${userId}`, data)
}

/**
 * 获取当前员工的待审核用户列表
 * @returns {Promise<ApiResult<Array<VideoUserResponseDto>>>} 待审核用户列表
 */
export function getPendingUsers () {
  return request.get('/UserAudit/pending-users')
}

/**
 * 批准用户
 * @param {number} userId - 用户ID
 * @param {string} [remark] - 审核备注
 * @returns {Promise<ApiResult<boolean>>} 审核结果
 */
export function approveUser (userId, remark = '') {
  return auditUser(userId, {
    status: 1,
    remark
  })
}

/**
 * 拒绝用户
 * @param {number} userId - 用户ID
 * @param {string} [remark] - 拒绝原因
 * @returns {Promise<ApiResult<boolean>>} 审核结果
 */
export function rejectUser (userId, remark = '') {
  return auditUser(userId, {
    status: 2,
    remark
  })
}

/**
 * 批量审核用户
 * @param {Array<{userId: number, status: number, remark?: string}>} auditList - 审核列表
 * @returns {Promise<Array<ApiResult<boolean>>>} 批量审核结果
 */
export function batchAuditUsers (auditList) {
  const promises = auditList.map(item =>
    auditUser(item.userId, {
      status: item.status,
      remark: item.remark || ''
    })
  )
  return Promise.all(promises)
}

/**
 * 批量批准用户
 * @param {Array<number>} userIds - 用户ID列表
 * @param {string} [remark] - 审核备注
 * @returns {Promise<Array<ApiResult<boolean>>>} 批量审核结果
 */
export function batchApproveUsers (userIds, remark = '') {
  const auditList = userIds.map(userId => ({
    userId,
    status: 1,
    remark
  }))
  return batchAuditUsers(auditList)
}

/**
 * 批量拒绝用户
 * @param {Array<number>} userIds - 用户ID列表
 * @param {string} [remark] - 拒绝原因
 * @returns {Promise<Array<ApiResult<boolean>>>} 批量审核结果
 */
export function batchRejectUsers (userIds, remark = '') {
  const auditList = userIds.map(userId => ({
    userId,
    status: 2,
    remark
  }))
  return batchAuditUsers(auditList)
}

/**
 * 获取审核状态选项
 * @returns {Array<{value: number, label: string, color: string}>} 审核状态选项
 */
export function getAuditStatusOptions () {
  return [
    { value: 0, label: '待审核', color: '#faad14' },
    { value: 1, label: '审核通过', color: '#52c41a' },
    { value: 2, label: '审核拒绝', color: '#f5222d' }
  ]
}

/**
 * 获取审核状态文本
 * @param {number} status - 状态值
 * @returns {string} 状态文本
 */
export function getAuditStatusText (status) {
  const options = getAuditStatusOptions()
  const option = options.find(opt => opt.value === status)
  return option ? option.label : '未知状态'
}

/**
 * 获取审核状态颜色
 * @param {number} status - 状态值
 * @returns {string} 状态颜色
 */
export function getAuditStatusColor (status) {
  const options = getAuditStatusOptions()
  const option = options.find(opt => opt.value === status)
  return option ? option.color : '#d9d9d9'
}

/**
 * 验证审核数据
 * @param {VideoUserAuditDto} data - 审核数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateAuditData (data) {
  const errors = []

  if (data.status === undefined || data.status === null) {
    errors.push('审核状态不能为空')
  } else if (![1, 2].includes(data.status)) {
    errors.push('审核状态必须为1(通过)或2(拒绝)')
  }

  if (data.status === 2 && (!data.remark || data.remark.trim() === '')) {
    errors.push('拒绝审核时必须填写拒绝原因')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 格式化待审核用户数据
 * @param {Array<VideoUserResponseDto>} users - 用户列表
 * @returns {Array<Object>} 格式化后的用户列表
 */
export function formatPendingUsers (users) {
  if (!Array.isArray(users)) return []

  return users.map(user => ({
    ...user,
    // 格式化时间
    lastLogin: user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '从未登录',
    createTime: user.createTime ? new Date(user.createTime).toLocaleString() : '',

    // 处理头像
    avatar: user.avatar || '/static/images/default-avatar.png',

    // 处理昵称
    nickname: user.nickname || '未设置昵称',

    // 处理员工信息
    employeeName: user.employeeName || '未绑定员工',

    // 添加审核状态
    auditStatus: 0, // 待审核
    auditStatusText: '待审核',
    auditStatusColor: '#faad14'
  }))
}

/**
 * 检查是否有待审核用户
 * @param {Array<VideoUserResponseDto>} users - 用户列表
 * @returns {boolean} 是否有待审核用户
 */
export function hasPendingUsers (users) {
  return Array.isArray(users) && users.length > 0
}

/**
 * 获取待审核用户数量
 * @param {Array<VideoUserResponseDto>} users - 用户列表
 * @returns {number} 待审核用户数量
 */
export function getPendingUserCount (users) {
  return Array.isArray(users) ? users.length : 0
}

/**
 * 生成审核操作日志
 * @param {number} userId - 用户ID
 * @param {number} status - 审核状态
 * @param {string} remark - 审核备注
 * @param {string} operatorName - 操作员姓名
 * @returns {Object} 审核日志
 */
export function generateAuditLog (userId, status, remark, operatorName) {
  return {
    userId,
    status,
    statusText: getAuditStatusText(status),
    remark,
    operatorName,
    operateTime: new Date().toISOString(),
    operateTimeText: new Date().toLocaleString()
  }
}

/**
 * 获取常用拒绝原因
 * @returns {Array<string>} 常用拒绝原因列表
 */
export function getCommonRejectReasons () {
  return [
    '用户信息不完整',
    '头像不符合要求',
    '昵称不符合规范',
    '疑似虚假用户',
    '重复注册',
    '其他原因'
  ]
}

// 默认导出所有用户审核相关API
export default {
  auditUser,
  getPendingUsers,
  approveUser,
  rejectUser,
  batchAuditUsers,
  batchApproveUsers,
  batchRejectUsers,
  getAuditStatusOptions,
  getAuditStatusText,
  getAuditStatusColor,
  validateAuditData,
  formatPendingUsers,
  hasPendingUsers,
  getPendingUserCount,
  generateAuditLog,
  getCommonRejectReasons
}
