/**
 * 应用配置文件
 * 管理API地址、环境配置等全局设置
 */

// 环境类型
export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 当前环境（可以通过构建工具或其他方式动态设置）
export const CURRENT_ENV = ENV_TYPES.DEVELOPMENT

// API配置
export const API_CONFIG = {
  // 不同环境的API地址
  BASE_URLS: {
    // [ENV_TYPES.DEVELOPMENT]: 'http://*************:8080/api',
    [ENV_TYPES.DEVELOPMENT]: 'https://localhost:7048/api',
    // [ENV_TYPES.DEVELOPMENT]: 'http://datamgrsystemdev.tr188.com/DataMgrSystem1/api',

    [ENV_TYPES.TEST]: '',
    [ENV_TYPES.PRODUCTION]: ''
  },

  // 请求配置
  TIMEOUT: 30000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,

  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

// 存储配置
export const STORAGE_CONFIG = {
  // 存储键名
  KEYS: {
    TOKEN: 'auth_token',
    USER_INFO: 'user_info',
    LOGIN_INFO: 'loginInfo',
    ADMIN_LOGIN_INFO: 'adminLoginInfo',
    USER_PERMISSIONS: 'userPermissions',
    APP_CONFIG: 'app_config'
  },

  // 存储过期时间（毫秒）
  EXPIRE_TIME: {
    TOKEN: 7 * 24 * 60 * 60 * 1000, // 7天
    USER_INFO: 24 * 60 * 60 * 1000,  // 1天
    CACHE: 30 * 60 * 1000            // 30分钟
  }
}

// 页面路由配置
export const ROUTE_CONFIG = {
  // 登录页面
  LOGIN: '/pages/login/index',

  // 主页面
  HOME: '/pages/index/index',

  // 管理页面
  ADMIN: '/pages/admin/index',

  // 用户页面
  USER: '/pages/user/index',

  // 视频页面
  VIDEO: '/pages/video/index'
}

// 用户权限配置
export const PERMISSION_CONFIG = {
  // 权限类型
  PERMISSIONS: {
    // 基础权限
    VIEW_VIDEOS: 'view_videos',
    TAKE_QUIZ: 'take_quiz',
    VIEW_REWARDS: 'view_rewards',

    // 管理权限
    VIEW_DASHBOARD: 'view_dashboard',
    MANAGE_USERS: 'manage_users',
    MANAGE_VIDEOS: 'manage_videos',
    MANAGE_QUIZ: 'manage_quiz',
    VIEW_REPORTS: 'view_reports',

    // 高级权限
    SYSTEM_CONFIG: 'system_config',
    USER_MANAGEMENT: 'user_management',
    DATA_EXPORT: 'data_export',

    // 超级管理员权限
    ALL: '*'
  },

  // 角色权限映射
  ROLE_PERMISSIONS: {
    employee: ['view_videos', 'take_quiz', 'view_rewards'],
    manager: ['view_dashboard', 'manage_users', 'view_reports', 'view_videos', 'take_quiz'],
    admin: ['*'],
    agent: ['view_dashboard', 'manage_employees', 'view_reports', 'manage_users'] // 管理角色权限
  }
}

// 应用配置
export const APP_CONFIG = {
  // 应用信息
  APP_NAME: '视频学习测验系统',
  VERSION: '1.0.0',

  // 功能开关
  FEATURES: {
    ENABLE_CACHE: true,          // 是否启用缓存
    ENABLE_OFFLINE: false,       // 是否启用离线模式
    ENABLE_ANALYTICS: false      // 是否启用数据分析
  },

  // UI配置
  UI: {
    // 主题色彩
    COLORS: {
      PRIMARY: '#186BFF',
      SUCCESS: '#52C41A',
      WARNING: '#FAAD14',
      ERROR: '#F5222D',
      INFO: '#186BFF'
    },

    // 页面配置
    PAGE_SIZE: 20,               // 默认分页大小
    MAX_UPLOAD_SIZE: 2 * 1024 * 1024 * 1024, // 最大上传文件大小 2GB

    // 动画配置
    ANIMATION_DURATION: 300      // 默认动画时长
  }
}

/**
 * 获取当前环境的API基础URL
 * @returns {string} API基础URL
 */
export function getApiBaseURL () {
  console.log('=== getApiBaseURL 调试信息 ===');
  console.log('CURRENT_ENV:', CURRENT_ENV);
  console.log('ENV_TYPES.DEVELOPMENT:', ENV_TYPES.DEVELOPMENT);
  console.log('API_CONFIG.BASE_URLS:', API_CONFIG.BASE_URLS);
  console.log('API_CONFIG.BASE_URLS[CURRENT_ENV]:', API_CONFIG.BASE_URLS[CURRENT_ENV]);
  console.log('API_CONFIG.BASE_URLS[ENV_TYPES.DEVELOPMENT]:', API_CONFIG.BASE_URLS[ENV_TYPES.DEVELOPMENT]);

  const result = API_CONFIG.BASE_URLS[CURRENT_ENV] || API_CONFIG.BASE_URLS[ENV_TYPES.DEVELOPMENT];
  console.log('getApiBaseURL 返回结果:', result);
  console.log('=== getApiBaseURL 调试结束 ===');

  return result;
}

/**
 * 获取完整的API URL
 * @param {string} endpoint API端点
 * @returns {string} 完整的API URL
 */
export function getApiURL (endpoint) {
  const baseURL = getApiBaseURL()
  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseURL}${normalizedEndpoint}`
}

/**
 * 检查功能是否启用
 * @param {string} feature 功能名称
 * @returns {boolean} 是否启用
 */
export function isFeatureEnabled (feature) {
  return APP_CONFIG.FEATURES[feature] || false
}

/**
 * 获取存储键名
 * @param {string} key 键名
 * @returns {string} 完整的存储键名
 */
export function getStorageKey (key) {
  return STORAGE_CONFIG.KEYS[key] || key
}

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export function isDevelopment () {
  return CURRENT_ENV === ENV_TYPES.DEVELOPMENT
}

/**
 * 检查是否为生产环境
 * @returns {boolean} 是否为生产环境
 */
export function isProduction () {
  return CURRENT_ENV === ENV_TYPES.PRODUCTION
}

// 默认导出配置对象
export default {
  ENV_TYPES,
  CURRENT_ENV,
  API_CONFIG,
  STORAGE_CONFIG,
  ROUTE_CONFIG,
  PERMISSION_CONFIG,
  APP_CONFIG,

  // 工具函数
  getApiBaseURL,
  getApiURL,
  isFeatureEnabled,
  getStorageKey,
  isDevelopment,
  isProduction
}
