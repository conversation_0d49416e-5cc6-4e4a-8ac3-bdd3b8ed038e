<template>
    <view class="data-page">


        <!-- 数据展示组件 -->
        <BatchDataDisplay :batchId="batchId" :batchData="batchData" />
    </view>
</template>

<script>
import { getBatchDetail } from "@/api/batch.js";
import BatchDataDisplay from "@/components/BatchDataDisplay.vue";

export default {
    name: 'BatchDataPage',
    components: {
        BatchDataDisplay
    },
    data () {
        return {
            batchId: '',
            batchData: {
                id: '',
                title: '',
                totalViews: 0,
                totalReward: 0,
                totalStudents: 0
            }
        }
    },
    async onLoad (options) {
        if (options && options.id) {
            this.batchId = options.id;
            await this.loadBatchData();
        } else {
            uni.showToast({
                title: '批次ID缺失',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    },
    methods: {
        async loadBatchData () {
            try {
                uni.showLoading({
                    title: "加载中...",
                });

                const response = await getBatchDetail(this.batchId);

                if (response.success && response.data) {
                    const batch = response.data;
                    this.batchData = {
                        id: batch.id,
                        title: batch.name || batch.title,
                        totalViews: batch.currentParticipants || 0,
                        totalReward: batch.rewardAmount || 0,
                        totalStudents: batch.totalStudents || batch.currentParticipants || 0,
                        redPacketAmount: batch.redPacketAmount || 0,
                        // 传递完整的统计数据
                        statistics: batch.statistics || null
                    };

                    console.log('批次数据加载完成:', this.batchData);
                } else {
                    throw new Error(response.msg || '获取批次数据失败');
                }

                uni.hideLoading();
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: "加载失败",
                    icon: "none",
                });
            }
        },
        goBack () {
            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.data-page {
    width: 100%;
    min-height: 100vh;
    background: #f8faff;
}

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #186BFF 0%, #40a9ff 100%);
    padding: 40rpx 32rpx 32rpx;
    color: white;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 24rpx;
}

.back-section {
    flex-shrink: 0;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    background: rgba(255, 255, 255, 0.2);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 12rpx;
    color: white;
    font-size: 28rpx;
    transition: all 0.3s ease;

    &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(1rpx);
    }
}

.back-icon {
    font-size: 32rpx;
    font-weight: bold;
}

.back-text {
    font-size: 28rpx;
    font-weight: 500;
}

.title-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6rpx;
}

.page-title {
    font-size: 36rpx;
    font-weight: 600;
    color: white;
}

.page-subtitle {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.9);
    opacity: 0.9;
}
</style>
