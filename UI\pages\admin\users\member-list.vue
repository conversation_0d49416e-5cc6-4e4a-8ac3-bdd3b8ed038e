<template>
  <view class="container">
    <!-- 管理者信息卡片 - 固定在顶部 -->
    <view class="manager-card fixed-header">
      <view class="custom-card">
        <!-- 管理者头像和基本信息 -->
        <view class="manager-header">
          <view class="manager-avatar-section">
            <u-avatar :src="managerInfo.avatar || '/assets/images/avatar-placeholder.png'"
              :text="managerInfo.username?.charAt(0)?.toUpperCase() || 'U'" size="60" shape="circle" bg-color="#f0f9ff"
              color="#186BFF" :randomBgColor="!managerInfo.avatar" />
          </view>

          <view class="manager-basic-info">
            <view class="manager-name-row">
              <text class="manager-name">{{ managerInfo.username }}</text>
              <u-tag :text="managerType === 'manager' ? '管理' : '员工'"
                :type="managerType === 'manager' ? 'warning' : 'info'" shape="circle" size="mini" />
            </view>

            <view class="manager-id">
              <text class="id-label">ID: </text>
              <text class="id-value">{{ managerInfo.id }}</text>
              <u-button type="primary" size="mini" shape="circle" @click.stop="copyManagerId"
                :customStyle="{ marginLeft: '16rpx' }">
                <u-icon name="copy" size="12" color="#fff" />
                <text style="margin-left: 8rpx; font-size: 20rpx;">复制</text>
              </u-button>
            </view>

            <!-- 管理者操作按钮 -->
            <view class="manager-actions">
              <u-button type="primary" size="small" shape="round" @click.stop="showManagerActionSheet" text="管理操作"
                :customStyle="{ marginTop: '16rpx' }" />
            </view>
          </view>
        </view>

        <!-- 详细信息区域 -->
        <view class="manager-details">
          <view class="detail-grid">
            <view class="detail-item">
              <u-icon name="calendar" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">注册时间</text>
                <text class="detail-value">{{ formatDate(managerInfo.registerTime) }}</text>
              </view>
            </view>
            <view class="detail-item">
              <u-icon name="clock" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">最后登录</text>
                <text class="detail-value">{{ formatDate(managerInfo.lastLoginTime) || '从未登录' }}</text>
              </view>
            </view>

            <view class="detail-item" v-if="managerType === 'manager'">
              <u-icon name="account" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">管理员工</text>
                <text class="detail-value">{{ activeEmployees.length }}人</text>
              </view>
            </view>
            <view class="detail-item" v-if="managerType === 'manager'">
              <u-icon name="account-fill" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">总用户数</text>
                <text class="detail-value">{{ filteredUsers.length }}人</text>
              </view>
            </view>

            <view class="detail-item" v-if="managerType === 'employee'">
              <u-icon name="level" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">推广用户</text>
                <text class="detail-value">{{ filteredUsers.length }}人</text>
              </view>
            </view>
            <view class="detail-item" v-if="managerType === 'employee'">
              <u-icon name="man" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">所属管理</text>
                <text class="detail-value">{{ getManagerName(managerInfo.managerId) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 管理者操作按钮已移至用户ID下方 -->
      </view>
    </view>

    <!-- 员工管理操作按钮 - 只在员工详情页显示 -->
    <!-- <view class="employee-actions" v-if="managerType === 'employee'">
        <button class="action-btn dismiss-btn" :disabled="managerInfo.dismissed" @tap="showDismissModal">
          {{ managerInfo.dismissed ? "已离职" : "员工离职" }}
        </button>
        <button class="action-btn transfer-btn" @tap="showTransferModal">
          用户转移
        </button>
      </view> -->

    <!-- 离职提示 -->
    <u-alert v-if="managerType === 'employee' && managerInfo && managerInfo.dismissed && filteredUsers.length > 0"
      type="warning" :show-icon="true" :closable="false" title="该员工已离职，建议将用户转移给其他员工"
      :description="`当前有 ${filteredUsers.length} 个用户需要转移`" :customStyle="{ margin: '20rpx' }">
      <template #icon>
        <u-icon name="error-circle" size="20" color="#faad14" />
      </template>
      <template #desc>
        <view style="margin-top: 16rpx;">
          <u-button type="warning" size="small" shape="round" @click="showTransferModal" text="立即转移"
            :customStyle="{ marginTop: '16rpx' }">
            <u-icon name="reload" size="14" color="#fff" style="margin-right: 8rpx;" />
          </u-button>
        </view>
      </template>
    </u-alert>

    <!-- 可滚动内容区域 -->
    <scroll-view class="scrollable-content" scroll-y="true">
      <!-- 时间筛选 -->
      <TimeFilter v-model="activeTimeFilter" @change="handleTimeFilterChange" />

      <!-- 员工列表 -->
      <view class="member-list" v-if="managerType === 'manager' && activeEmployees.length > 0">


        <!-- 在职员工列表 -->
        <view class="member-cards-container">
          <view class="member-card" v-for="employee in activeEmployees" :key="employee.id"
            @tap="viewEmployeeDetail(employee)">
            <UserInfoCard :userInfo="formattedEmployeeInfo(employee)" :timeFilter="activeTimeFilter"
              :customDateRange="customDateRange" :showDetailBtn="false" :showFooterBtns="false" />
          </view>
        </view>

        <!-- 已离职员工折叠区域 -->
        <u-collapse v-if="dismissedEmployees.length > 0" :customStyle="{ margin: '20rpx 0' }">
          <u-collapse-item :title="`已离职员工 (${dismissedEmployees.length})`" name="dismissed"
            :icon="{ name: 'account', color: '#909399' }">
            <view class="dismissed-content">
              <view class="member-card dismissed-card" v-for="employee in dismissedEmployees" :key="employee.id"
                @tap="viewEmployeeDetail(employee)">
                <UserInfoCard :userInfo="formattedEmployeeInfo(employee)" :timeFilter="activeTimeFilter"
                  :customDateRange="customDateRange" :showDetailBtn="false" :showFooterBtns="false" />
                <u-tag text="已离职" type="warning" shape="circle" size="mini" :customStyle="{
                  position: 'absolute',
                  top: '16rpx',
                  right: '16rpx',
                  zIndex: 10
                }">
                  <template #icon>
                    <u-icon name="error-circle" size="12" color="#faad14" />
                  </template>
                </u-tag>
              </view>
            </view>
          </u-collapse-item>
        </u-collapse>
      </view>

      <!-- 用户列表 - 只在员工详情页显示 -->
      <view class="member-list" v-if="managerType === 'employee'">


        <view class="member-cards-container">
          <view class="member-card" v-for="user in filteredUsers" :key="user.id" @tap="viewUserDetail(user)">
            <UserInfoCard :userInfo="formattedUserInfo(user)" :showDetailBtn="false" :showFooterBtns="false" />
          </view>
        </view>
      </view>







      <!-- 密码重置弹窗 -->
      <u-modal v-model:show="showPasswordModal" title="重置密码" :closeOnClickOverlay="true" @close="closePasswordModal"
        :customStyle="{ maxWidth: '600rpx' }" :showCancelButton="false" :showConfirmButton="false">
        <view class="modal-form">
          <!-- 警告提示 -->
          <u-alert type="warning" :show-icon="true" :closable="false" title="重置密码将覆盖用户当前密码，请谨慎操作"
            :customStyle="{ marginBottom: '32rpx' }" />

          <u-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" labelWidth="120" labelPosition="top">
            <u-form-item label="新密码" prop="newPassword" :required="true">
              <u-input v-model="passwordForm.newPassword" placeholder="请输入新密码" type="password" border="surround"
                clearable :customStyle="{ width: '100%' }" />
            </u-form-item>

            <u-form-item label="确认密码" prop="confirmPassword" :required="true">
              <u-input v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" type="password" border="surround"
                clearable :customStyle="{ width: '100%' }" />
            </u-form-item>
          </u-form>

          <!-- 密码要求提示 -->
          <view class="password-tips">
            <text class="tip-title">密码要求：</text>
            <text class="tip-item">• 长度不少于6位</text>
            <text class="tip-item">• 必须包含字母和数字</text>
            <text class="tip-item">• 建议包含特殊字符</text>
          </view>

          <view class="modal-buttons">
            <u-button type="info" @click="closePasswordModal" text="取消" size="large"
              :customStyle="{ width: '45%', marginRight: '10%' }" />
            <u-button type="primary" @click="confirmResetPassword" :loading="loading" text="确认重置" size="large"
              :customStyle="{ width: '45%' }" />
          </view>
        </view>
      </u-modal>



      <!-- 管理者操作底部弹出层 -->
      <u-action-sheet v-model:show="showManagerActionSheetVisible" :actions="managerActionList" title="管理操作"
        @select="handleManagerAction" @close="closeManagerActionSheet" :closeOnClickOverlay="true"
        :safeAreaInsetBottom="true" />
    </scroll-view>
  </view>
</template>

<script>

import UserInfoCard from "../../../components/UserInfoCard.vue";
import TimeFilter from "../../../components/TimeFilter.vue";
import {
  toggleSysUserStatus,
  resetSysUserPassword,
  getSysUserSubordinatesById,
  getSysUserDetail
} from "../../../api/sysuser.js";
import {
  getEmployeeDetail
} from "../../../api/employee.js";
import {
  queryUsersByEmployee
} from "../../../api/video-user.js";
import { formatSysUserData, formatEmployeeData, formatUserData } from "../../../utils/employee-data-mapper.js";
import { apiCallWrapper, ErrorHandlerPresets } from "../../../utils/api-error-handler.js";
import { formatUser, formatDate as formatDateUtil } from "../../../utils/data-formatter.js";
import CryptoJS from 'crypto-js';

export default {
  components: {
    UserInfoCard,
    TimeFilter,
  },
  data () {
    return {
      managerId: null,
      managerType: "", // 'manager' or 'employee'
      managerInfo: {
        id: '',
        username: '加载中...',
        avatar: '',
        registerTime: '',
        lastLoginTime: '',
        managerId: '',
        disabled: false,
        dismissed: false,
        type: ''
      },
      employees: [],
      users: [],
      searchKeyword: "",
      activeTimeFilter: "today", // 'today', 'yesterday', 'thisWeek', 'thisMonth', 'custom'
      customDateRange: {
        startDate: "",
        endDate: "",
      },
      currentUserRole: "admin", // 'admin', 'manager', 'employee'

      // 已离职员工折叠状态
      showDismissedEmployees: false,
      // 密码管理相关
      showPasswordModal: false,
      passwordForm: {
        newPassword: '',
        confirmPassword: ''
      },
      // 密码表单验证规则
      passwordRules: {
        newPassword: [
          {
            required: true,
            message: '请输入新密码',
            trigger: ['blur', 'change']
          },
          {
            min: 6,
            max: 20,
            message: '密码长度在 6 到 20 个字符',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
            message: '密码必须包含字母和数字',
            trigger: ['blur', 'change']
          }
        ],
        confirmPassword: [
          {
            required: true,
            message: '请确认密码',
            trigger: ['blur', 'change']
          },
          {
            validator: (_, value, callback) => {
              if (value !== this.passwordForm.newPassword) {
                callback(new Error('两次输入的密码不一致'))
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change']
          }
        ]
      },
      currentUser: null,
      // 底部弹出层相关
      showManagerActionSheetVisible: false,
      loading: false,
    };
  },
  computed: {
    pageTitle () {
      if (this.managerType === "manager") {
        return `${this.managerInfo.username}的成员`;
      } else {
        return `${this.managerInfo.username}的用户`;
      }
    },

    // 管理者操作列表
    managerActionList () {
      const actions = [
        {
          name: this.managerInfo.disabled ? '启用账号' : '禁用账号',
          value: 'toggleStatus',
          color: this.managerInfo.disabled ? '#52c41a' : '#ff4d4f'
        },
        {
          name: '重置密码',
          value: 'resetPassword',
          color: '#1890ff'
        }
      ];

      // 员工特有操作
      if (this.managerType === 'employee') {
        actions.push({
          name: this.managerInfo.dismissed ? '恢复在职' : '员工离职',
          value: 'toggleDismiss',
          color: this.managerInfo.dismissed ? '#52c41a' : '#faad14'
        });
        actions.push({
          name: '用户转移',
          value: 'transferUser',
          color: '#722ed1'
        });
      }

      return actions;
    },

    // 获取当前员工的所有员工（包括已离职）
    allEmployees () {
      if (this.managerType !== "manager" || !this.employees.length) return [];

      let employeeList = this.employees.filter(
        (employee) => employee.managerId === this.managerId
      );

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        employeeList = employeeList.filter(
          (employee) =>
            employee.username.toLowerCase().includes(keyword) ||
            (employee.phone && employee.phone.includes(keyword))
        );
      }

      return employeeList;
    },

    // 获取当前员工的所有在职员工
    activeEmployees () {
      return this.allEmployees.filter((employee) => !employee.dismissed);
    },

    // 获取当前员工的所有已离职员工
    dismissedEmployees () {
      return this.allEmployees.filter((employee) => employee.dismissed);
    },

    filteredUsers () {
      let userList = this.users;

      // 如果是管理，显示所有下属员工的用户
      if (this.managerType === "manager") {
        const employeeIds = this.employees
          .filter((employee) => employee.managerId === this.managerId)
          .map((employee) => employee.id);

        userList = userList.filter((user) =>
          employeeIds.includes(user.employeeId)
        );
      }
      // 如果是员工，只显示该员工的用户
      else if (this.managerType === "employee") {
        userList = userList.filter(
          (user) => user.employeeId === this.managerId
        );
      }

      // 搜索过滤
      if (!this.searchKeyword) {
        return userList;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return userList.filter(
        (user) =>
          user.username.toLowerCase().includes(keyword) ||
          (user.phone && user.phone.includes(keyword))
      );
    },
  },
  async onLoad (options) {
    // 获取管理者ID和类型
    if (options.id) {
      // 所有ID都保持字符串格式，因为新的SysUser API使用字符串ID
      this.managerId = options.id;
      this.managerType = options.type || "employee";
    }

    // 加载数据
    await this.loadManagerInfo();
    await this.loadSubordinates();

    // 模拟当前用户角色
    this.currentUserRole = this.managerType; // 假设当前用户就是查看的管理者
  },
  methods: {
    // 加载管理者信息
    async loadManagerInfo () {
      if (!this.managerId) return;

      try {
        let response;

        if (this.managerType === 'manager') {
          // 管理员使用 SysUser API
          response = await apiCallWrapper(
            () => getSysUserDetail(this.managerId),
            {
              ...ErrorHandlerPresets.important,
              loadingTitle: '加载管理者信息...',
              errorTitle: '加载失败'
            }
          );

          if (response.success && response.data) {
            this.managerInfo = formatSysUserData(response.data);
            this.managerInfo.type = this.managerType;
          }
        } else {
          // 员工先尝试 SysUser API，如果失败再尝试 Employee API
          try {
            response = await apiCallWrapper(
              () => getSysUserDetail(this.managerId),
              {
                ...ErrorHandlerPresets.silent
              }
            );

            if (response.success && response.data) {
              this.managerInfo = formatSysUserData(response.data);
              this.managerInfo.type = this.managerType;
            }
          } catch (sysUserError) {
            // SysUser API 失败，尝试 Employee API
            console.log('SysUser API 失败，尝试 Employee API');
            response = await apiCallWrapper(
              () => getEmployeeDetail(this.managerId),
              {
                ...ErrorHandlerPresets.important,
                loadingTitle: '加载员工信息...',
                errorTitle: '加载失败'
              }
            );

            if (response.success && response.data) {
              this.managerInfo = formatEmployeeData(response.data);
              this.managerInfo.type = this.managerType;
            }
          }
        }
      } catch (error) {
        console.error('加载管理者信息失败:', error);
        // 设置默认值，避免空指针错误
        this.managerInfo = {
          id: this.managerId || '',
          username: '加载失败',
          avatar: '',
          registerTime: '',
          lastLoginTime: '',
          managerId: '',
          disabled: false,
          dismissed: false,
          type: this.managerType
        };
      }
    },

    // 加载下级成员
    async loadSubordinates () {
      try {
        if (!this.managerId) return;

        if (this.managerType === "manager") {
          // 如果是管理，加载其下级员工
          await this.loadEmployees();
        } else {
          // 如果是员工，加载其管理的用户
          await this.loadUsers();
        }
      } catch (error) {
        console.error('加载下级成员失败:', error);

        // API失败时显示空列表
        this.employees = [];
        this.users = [];
      }
    },

    // 加载员工列表
    async loadEmployees () {


      try {
        const response = await apiCallWrapper(
          () => getSysUserSubordinatesById(this.managerId, {
            pageIndex: 1,
            pageSize: 100
          }),
          {
            ...ErrorHandlerPresets.silent
          }
        );

        if (response.success && response.data) {
          this.employees = response.data.map(employee => formatSysUserData(employee));
        }
      } catch (error) {
        console.error('加载员工列表失败:', error);
      }
    },

    // 加载用户列表
    async loadUsers () {
      try {
        const response = await apiCallWrapper(
          () => queryUsersByEmployee(this.managerId, {
            PageIndex: 1,
            PageSize: 100
          }),
          {
            ...ErrorHandlerPresets.silent
          }
        );

        if (response.success && response.data) {
          this.users = response.data.items.map(user => formatUserData(user));
        }
      } catch (error) {
        console.error('加载用户列表失败:', error);
      }
    },

    // 处理时间筛选变化
    handleTimeFilterChange (timeRange) {
      this.activeTimeFilter = timeRange;
      this.customDateRange = {
        startDate: timeRange.startDate,
        endDate: timeRange.endDate
      };
      console.log("时间筛选变化:", timeRange);
      // 这里可以根据需要执行其他操作，如重新加载数据等
    },

    // 格式化用户信息，添加类型标识
    formattedUserInfo (user) {
      return {
        ...user,
        type: "user",
        // 确保有disabled属性，如果没有则默认为false
        disabled: user.disabled || user.status === 0 || false,
      };
    },

    // 格式化员工信息，添加类型标识
    formattedEmployeeInfo (employee) {
      return {
        ...employee,
        type: "employee",
        // 确保有disabled属性，如果没有则默认为false
        disabled: employee.disabled || employee.status === 0 || false,
      };
    },



    viewUserDetail (user) {
      uni.navigateTo({
        url: `/pages/admin/users/info?userId=${user.id}`,
      });
    },

    viewEmployeeDetail (employee) {
      uni.navigateTo({
        url: `/pages/admin/users/member-list?id=${employee.id}&type=employee`,
      });
    },

    goBack () {
      uni.navigateBack();
    },



    // 密码管理相关方法
    handleChangePassword (item) {
      console.log("修改密码:", item);
      this.currentUser = item;
      this.showPasswordModal = true;
    },

    closePasswordModal () {
      this.showPasswordModal = false;
      this.passwordForm = {
        newPassword: '',
        confirmPassword: ''
      };
      this.currentUser = null;
      // 重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.passwordForm) {
          this.$refs.passwordForm.resetFields();
        }
      });
    },



    async confirmResetPassword () {
      // 使用 uview-plus 表单验证
      try {
        const valid = await this.$refs.passwordForm.validate();
        if (!valid) {
          return;
        }
      } catch (error) {
        // 验证失败
        return;
      }

      try {
        this.loading = true;

        // 对新密码进行MD5加密
        const hashedNewPassword = CryptoJS.MD5(this.passwordForm.newPassword).toString()

        await resetSysUserPassword({
          userId: this.currentUser.id,
          newPassword: hashedNewPassword
        })

        uni.showToast({
          title: '密码重置成功',
          icon: 'success'
        })

        this.closePasswordModal()
      } catch (error) {
        uni.showToast({
          title: error.message || '密码重置失败',
          icon: 'none'
        })
      } finally {
        this.loading = false;
      }
    },







    // 复制管理者ID
    copyManagerId () {
      if (this.managerInfo.id) {
        uni.setClipboardData({
          data: this.managerInfo.id.toString(),
          success: () => {
            uni.showToast({
              title: 'ID已复制',
              icon: 'success'
            });
          }
        });
      }
    },

    // 获取管理名称
    getManagerName (managerId) {
      if (!managerId) return '未分配';
      // TODO: 从API获取管理者信息
      return '未知管理';
    },

    // 格式化日期
    formatDate (dateString) {
      return formatDateUtil(dateString, 'datetime');
    },

    // === 底部弹出层相关方法 ===
    // 显示管理者操作弹出层
    showManagerActionSheet () {
      this.showManagerActionSheetVisible = true;
    },

    // 关闭管理者操作弹出层
    closeManagerActionSheet () {
      this.showManagerActionSheetVisible = false;
    },

    // 处理管理者操作选择
    handleManagerAction (action) {
      this.closeManagerActionSheet();

      switch (action.value) {
        case 'toggleStatus':
          this.handleManagerToggleStatus();
          break;
        case 'resetPassword':
          this.handleManagerResetPassword();
          break;
        case 'toggleDismiss':
          this.handleEmployeeDismiss();
          break;
        case 'transferUser':
          this.handleUserTransfer();
          break;
      }
    },

    // === 管理者操作相关方法 ===
    // 处理管理者状态切换
    async handleManagerToggleStatus () {
      this.closeManagerActionSheet();

      const action = this.managerInfo.disabled ? '启用' : '禁用';
      const status = this.managerInfo.disabled ? 1 : 0;
      const userType = this.managerType === 'manager' ? '管理' : '员工';

      uni.showModal({
        title: `确认${action}`,
        content: `确定要${action}${userType} ${this.managerInfo.username} 的账号吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              await toggleSysUserStatus(this.managerInfo.id, status);
              this.managerInfo.disabled = !this.managerInfo.disabled;

              uni.showToast({
                title: `账号已${action}`,
                icon: 'success'
              });
            } catch (error) {
              uni.showToast({
                title: error.message || `${action}失败`,
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 处理管理者密码重置
    handleManagerResetPassword () {
      this.closeManagerActionSheet();
      this.currentUser = this.managerInfo;
      this.showPasswordModal = true;
    },

    // 处理员工离职/恢复
    handleEmployeeDismiss () {
      this.closeManagerActionSheet();
      if (this.managerInfo.dismissed) {
        // 恢复在职
        uni.showModal({
          title: '确认恢复',
          content: `确定要恢复员工 ${this.managerInfo.username} 的在职状态吗？`,
          success: (res) => {
            if (res.confirm) {
              this.managerInfo.dismissed = false;
              // 更新本地数据
              const employeeIndex = this.employees.findIndex(emp => emp.id === this.managerId);
              if (employeeIndex !== -1) {
                this.employees[employeeIndex].dismissed = false;
              }
              uni.showToast({
                title: '员工已恢复在职',
                icon: 'success'
              });
            }
          }
        });
      } else {
        // 员工离职 - 简化处理
        uni.showModal({
          title: '确认离职',
          content: `确定要将员工 ${this.managerInfo.username} 设为离职状态吗？`,
          success: (res) => {
            if (res.confirm) {
              this.managerInfo.dismissed = true;
              uni.showToast({
                title: '员工已设为离职',
                icon: 'success'
              });
            }
          }
        });
      }
    },

    // 处理用户转移
    handleUserTransfer () {
      this.closeManagerActionSheet();
      uni.showToast({
        title: '用户转移功能开发中',
        icon: 'none'
      });
    },
  },
};
</script>

<style lang="scss">
@import '@/styles/index.scss';

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-secondary;
  /* 消除头部空白 */
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 固定头部 */
.fixed-header {
  position: relative;
  z-index: 100;
  flex-shrink: 0;
}

/* 管理者卡片容器 */
.manager-card {
  /* 确保紧贴顶部 */
  margin: 0;
  padding: 0;
}

/* 自定义卡片样式 - 替代 u-card */
.custom-card {
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  margin: 16rpx;
  overflow: hidden;
  /* 消除顶部空白 */
  margin-top: 0;
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  height: 0;
}

/* 管理者信息样式 */
.manager-header {
  display: flex;
  padding: $spacing-base $spacing-lg;
  align-items: center;
}

.manager-avatar-section {
  margin-right: $spacing-base;
}

.manager-basic-info {
  flex: 1;
}

.manager-name-row {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.manager-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-primary;
  margin-right: $spacing-base;
}

.manager-id {
  display: flex;
  align-items: center;
}

.manager-actions {
  margin-top: $spacing-sm;
}

.id-label {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.id-value {
  font-size: $font-size-sm;
  color: $text-primary;
  margin-right: $spacing-sm;
}

/* 详细信息区域 */
.manager-details {
  padding: 0 $spacing-lg $spacing-base;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: $spacing-sm;
  background: $bg-primary;
  border-radius: $border-radius-base;
  border: 1rpx solid $border-secondary;
}

.detail-content {
  flex: 1;
  margin-left: $spacing-sm;
}

.detail-label {
  display: block;
  font-size: $font-size-xs;
  color: $text-tertiary;
  margin-bottom: 2rpx;
}

.detail-value {
  display: block;
  font-size: $font-size-sm;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

/* 成员列表样式 */
.member-list {
  padding: $spacing-sm $spacing-base;
}

.member-cards-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.member-card {
  position: relative;
  background: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
  border: 1rpx solid $border-secondary;
}

/* 离职员工卡片样式 */
.dismissed-card {
  opacity: 0.9;
  background: rgba(248, 249, 250, 0.8);
  border: 1rpx dashed $border-primary;
}

.dismissed-content {
  padding: $spacing-sm;
}

/* 密码提示样式 */
.password-tips {
  margin-top: $spacing-base;
  padding: $spacing-sm;
  background: $bg-tertiary;
  border-radius: $border-radius-base;
  border: 1rpx solid $border-secondary;
}

.tip-title {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin-bottom: $spacing-xs;
}

.tip-item {
  display: block;
  font-size: $font-size-xs;
  color: $text-secondary;
  margin-bottom: 2rpx;
}

.modal-form {
  padding: $spacing-sm 0;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: $spacing-lg;
  padding: 0 $spacing-sm;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: $spacing-base;
  }

  .manager-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .manager-avatar-section {
    margin-right: 0;
    margin-bottom: $spacing-lg;
  }
}
</style>