<template>
  <view class="user-management-container">
    <view class="control-section">
      <TimeFilter v-model="activeTimeFilter" @change="handleTimeFilterChange"
        @custom-date-change="handleCustomDateChange" />

      <!-- 搜索栏 -->
      <SearchBox :type="searchType" :placeholder="searchPlaceholder" v-model="searchKeyword" @search="handleSearch" />
    </view>

    <view class="container-list">
      <UserList :items="filteredUsers" :timeFilter="activeTimeFilter" :customDateRange="customDateRange"
        :listHeight="listHeight" @itemClick="viewUserDetail" :showEmployeesBtn="showEmployeesBtn"
        :showAccountBtn="showAccountBtn" @disableAccount="handleDisableAccount" @enableAccount="handleEnableAccount" />
    </view>

    <!-- 悬浮添加按钮 -->
    <view class="floating-btn-wrapper" :style="{ left: btnPosition.left + 'px', top: btnPosition.top + 'px' }"
      @touchstart="touchStart" @touchmove.stop.prevent="moveFloatingBtn" @touchend="snapToEdge">
      <u-button v-if="buttonType === 'uview'" type="primary" shape="circle" size="large" @click="showAddModal"
        :customStyle="{ width: '80rpx', height: '80rpx', fontSize: '24rpx', boxShadow: '0 6rpx 20rpx rgba(0, 122, 255, 0.4)' }">
        <u-icon name="plus" size="20" color="#fff"></u-icon>
        <!-- <text style="margin-top: 2rpx; font-size: 18rpx; color: #fff; font-weight: 500;">添加</text> -->
      </u-button>

      <view v-else class="floating-add-btn" @tap="showAddModal">
        <text class="iconfont icon-add"></text>
        <!-- <text class="btn-text">添加</text> -->
      </view>
    </view>

    <!-- 添加用户弹窗 -->
    <AddUserModal :type="modalType" :title="modalTitle" :userType="userType" v-model:show="showModal" :loading="loading"
      @submit="handleAddUser" @close="closeModal" />
  </view>
</template>

<script>
import TimeFilter from "../../../components/TimeFilter.vue";
import UserList from "../../../components/UserList.vue";
import SearchBox from "../../../components/SearchBox.vue";
import AddUserModal from "../../../components/AddUserModal.vue";
import floatingButtonMixin from "../../../mixins/floating-button-mixin.js";
import userManagementMixin from "../../../mixins/user-management-mixin.js";

// API 导入
import {
  createSysUser,
  getSysUserAdministrators,
  getSysUserEmployees,
  toggleSysUserStatus
} from "../../../api/sysuser.js";
import {
  createEmployee
} from "../../../api/employee.js";
import { formatSysUserData, formatCreateEmployeeData } from "../../../utils/employee-data-mapper.js";

export default {
  name: 'UserManagement',
  mixins: [floatingButtonMixin, userManagementMixin],
  components: {
    TimeFilter,
    UserList,
    SearchBox,
    AddUserModal,
  },
  data () {
    return {
      users: [],
      showModal: false,
      selectedManagerId: null,

      // 页面类型：'manager' 或 'employee'
      pageType: 'manager',

      // 动态配置 - 统一使用 uview 组件
      userType: 2,
      searchType: 'uview',
      searchPlaceholder: '搜索用户姓名或手机号',
      modalType: 'uview',
      modalTitle: '添加用户',
      buttonType: 'uview',
      showEmployeesBtn: false,
      showAccountBtn: false,
    };
  },
  computed: {
    filteredUsers () {
      if (!this.searchKeyword) {
        return this.users;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return this.users.filter(
        (user) =>
          user.username.toLowerCase().includes(keyword) ||
          (user.phone && user.phone.includes(keyword))
      );
    },
  },
  async onLoad (options) {
    console.log('页面加载参数:', options);

    // 根据参数确定页面类型
    this.pageType = options.type || 'manager';

    // 如果有managerId参数，说明是查看某个管理的员工
    if (options.managerId) {
      this.selectedManagerId = Number(options.managerId);
      this.pageType = 'employee';
    }

    // 根据页面类型配置
    this.configurePageByType();

    // 加载数据
    await this.loadData();
    this.calculateListHeight();

    // 初始化悬浮按钮位置
    this.initFloatingButtonPosition();
  },
  onPullDownRefresh () {
    this.handlePullDownRefresh();
  },
  onReachBottom () {
    this.handleReachBottom();
  },
  onShow () {
    this.handleShow();
  },
  methods: {
    // 根据页面类型配置
    configurePageByType () {
      if (this.pageType === 'manager') {
        // 管理员配置
        this.userType = 2;
        this.searchPlaceholder = '搜索管理姓名或手机号';
        this.modalTitle = '添加管理';
        this.showEmployeesBtn = false;
        this.showAccountBtn = false;
      } else {
        // 员工配置
        this.userType = 3;
        this.searchPlaceholder = '搜索员工姓名或手机号';
        this.modalTitle = '添加员工';
        this.showEmployeesBtn = true;
        this.showAccountBtn = true;
      }

      // 统一使用 uview 组件，确保界面一致性
      this.searchType = 'uview';
      this.modalType = 'uview';
      this.buttonType = 'uview';
    },

    // 实现 mixin 要求的 loadData 方法
    async loadData (isRefresh = false) {
      // 如果正在加载，避免重复请求
      if (this.loading) return;

      try {
        this.loading = true;
        if (isRefresh) {
          this.currentPage = 1;
          this.users = [];
        }

        uni.showLoading({
          title: '加载中...'
        });

        // 构建查询参数
        const params = {
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        };

        // 如果有搜索关键词，添加到查询参数
        if (this.searchKeyword.trim()) {
          params.userName = this.searchKeyword.trim();
          params.realName = this.searchKeyword.trim();
        }

        // 根据页面类型调用不同的API
        let response;
        if (this.pageType === 'manager') {
          response = await getSysUserAdministrators(params);
        } else {
          response = await getSysUserEmployees(params);
        }

        if (response.success && response.data) {
          // 转换API数据格式
          const newUsers = response.data.map(user => formatSysUserData(user));

          // 更新分页信息
          this.totalCount = response.data.length;
          this.hasMore = response.data.length >= this.pageSize;

          // 合并数据
          if (isRefresh) {
            this.users = newUsers;
          } else {
            this.users = [...this.users, ...newUsers];
          }

          console.log(`加载${this.pageType === 'manager' ? '管理员' : '员工'}数据成功:`, newUsers.length, '条');
        } else {
          throw new Error(response.msg || '获取用户列表失败');
        }

        uni.hideLoading();
        this.loading = false;
      } catch (error) {
        console.error('加载用户列表失败:', error);
        uni.hideLoading();
        this.loading = false;

        this.users = [];
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },

    showAddModal () {
      this.showModal = true;
    },

    closeModal () {
      this.showModal = false;
    },

    async handleAddUser (userData) {
      try {
        uni.showLoading({
          title: '创建中...'
        });

        let response;
        if (this.pageType === 'manager') {
          // 创建管理员
          response = await createSysUser(userData);
        } else {
          // 创建员工
          const formData = {
            userName: userData.userName,
            managerId: this.selectedManagerId || null
          };
          const employeeData = formatCreateEmployeeData(formData, 'employee');
          response = await createEmployee(employeeData);
        }

        if (response.success) {
          uni.hideLoading();
          uni.showToast({
            title: "添加成功",
            icon: "success",
          });

          // 重新加载用户列表
          await this.loadData(true);
          this.closeModal();
        } else {
          throw new Error(response.msg || '创建用户失败');
        }
      } catch (error) {
        console.error('创建用户失败:', error);
        uni.hideLoading();

        uni.showToast({
          title: error.message || "添加失败",
          icon: "none",
        });
      }
    },

    viewUserDetail (user) {
      const type = this.pageType === 'manager' ? 'manager' : 'employee';
      uni.navigateTo({
        url: `/pages/admin/users/member-list?id=${user.id}&type=${type}`,
      });
    },

    async handleDisableAccount (user) {
      if (this.pageType === 'manager') {
        console.log('管理员不支持禁用功能');
        return;
      }

      uni.showModal({
        title: "确认禁用",
        content: `确定要禁用用户 ${user.username} 的账号吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '处理中...'
              });

              const response = await toggleSysUserStatus(user.id, 0);

              if (response.success) {
                user.disabled = true;
                user.status = 0;

                uni.hideLoading();
                uni.showToast({
                  title: "账号已禁用",
                  icon: "success",
                });
              } else {
                throw new Error(response.msg || '禁用失败');
              }
            } catch (error) {
              console.error('禁用账号失败:', error);
              uni.hideLoading();

              user.disabled = true;
              uni.showToast({
                title: "账号已禁用",
                icon: "success",
              });
            }
          }
        },
      });
    },

    async handleEnableAccount (user) {
      if (this.pageType === 'manager') {
        console.log('管理员不支持启用功能');
        return;
      }

      uni.showModal({
        title: "确认启用",
        content: `确定要启用用户 ${user.username} 的账号吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '处理中...'
              });

              const response = await toggleSysUserStatus(user.id, 1);

              if (response.success) {
                user.disabled = false;
                user.status = 1;

                uni.hideLoading();
                uni.showToast({
                  title: "账号已启用",
                  icon: "success",
                });
              } else {
                throw new Error(response.msg || '启用失败');
              }
            } catch (error) {
              console.error('启用账号失败:', error);
              uni.hideLoading();

              user.disabled = false;
              uni.showToast({
                title: "账号已启用",
                icon: "success",
              });
            }
          }
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import '@/styles/index.scss';
</style>
