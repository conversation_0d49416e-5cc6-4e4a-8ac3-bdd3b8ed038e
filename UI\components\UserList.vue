<template>
    <view class="list-container" :style="{ height: listHeight ? listHeight + 'px' : 'calc(100vh - 380rpx)' }">
        <scroll-view class="scroll-list" scroll-y>
            <view class="user-card" v-for="item in items" :key="item.id" @tap="handleItemClick(item)">
                <UserInfoCard :userInfo="item" :timeFilter="timeFilter" :customDateRange="customDateRange"
                    :showDetailBtn="false" :showFooterBtns="false" :showEmployeesBtn="showEmployeesBtn"
                    :showAccountBtn="showAccountBtn" @disableAccount="handleDisableAccount"
                    @enableAccount="handleEnableAccount" />
            </view>
            <view class="list-bottom-space"></view>
        </scroll-view>
    </view>
</template>

<script>
import UserInfoCard from './UserInfoCard.vue'

export default {
    name: 'UserList',
    components: {
        UserInfoCard
    },
    props: {
        items: {
            type: Array,
            default: () => []
        },
        timeFilter: {
            type: String,
            default: 'today'
        },
        customDateRange: {
            type: Object,
            default: () => ({
                startDate: '',
                endDate: ''
            })
        },
        listHeight: {
            type: Number,
            default: 0
        },
        showEmployeesBtn: {
            type: Boolean,
            default: true
        },
        showAccountBtn: {
            type: Boolean,
            default: true
        }
    },
    methods: {
        handleItemClick (item) {
            this.$emit('itemClick', item)
        },
        handleDisableAccount (item) {
            this.$emit('disableAccount', item)
        },
        handleEnableAccount (item) {
            this.$emit('enableAccount', item)
        }
    }
}
</script>

<style>
.list-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: #F5F5F5;
    margin-top: 260rpx;
    /* margin: 0 16rpx; */
    /* margin-top: 160rpx; */
    /* padding-top: 7.8rem; */
}

.scroll-list {
    height: 100%;
    /* padding: 8rpx 0 120rpx 0; */
    box-sizing: border-box;
}

.user-card {
    display: block !important;
    position: relative;
    transition: transform 0.2s;
    margin-bottom: 12rpx;
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.user-card:active {
    transform: scale(0.98);
}

.list-bottom-space {
    height: 120rpx;
}
</style>