/**
 * 员工管理相关API
 * 处理员工信息管理、权限控制、业绩统计等功能
 */

import request from '../utils/request.js'

/**
 * 员工创建请求参数
 * @typedef {Object} EmployeeCreateDto
 * @property {string} employeeId - 员工ID
 * @property {string} name - 员工姓名
 * @property {string} phone - 手机号
 * @property {string} [email] - 邮箱
 * @property {string} [avatar] - 头像URL
 * @property {string} department - 部门
 * @property {string} position - 职位
 * @property {number} level - 级别
 * @property {string} [managerId] - 上级管理员ID
 * @property {number} status - 状态 (1:在职 2:离职 3:停职)
 * @property {string} [remark] - 备注
 */

/**
 * 员工更新请求参数
 * @typedef {Object} EmployeeUpdateDto
 * @property {string} employeeId - 员工ID
 * @property {string} [name] - 员工姓名
 * @property {string} [phone] - 手机号
 * @property {string} [email] - 邮箱
 * @property {string} [avatar] - 头像URL
 * @property {string} [department] - 部门
 * @property {string} [position] - 职位
 * @property {number} [level] - 级别
 * @property {string} [managerId] - 上级管理员ID
 * @property {number} [status] - 状态
 * @property {string} [remark] - 备注
 */

/**
 * 员工查询参数
 * @typedef {Object} EmployeeQueryDto
 * @property {string} [name] - 员工姓名
 * @property {string} [phone] - 手机号
 * @property {string} [department] - 部门
 * @property {string} [position] - 职位
 * @property {number} [level] - 级别
 * @property {string} [managerId] - 上级管理员ID
 * @property {number} [status] - 状态
 * @property {string} [startTime] - 开始时间
 * @property {string} [endTime] - 结束时间
 * @property {number} [pageIndex] - 页码
 * @property {number} [pageSize] - 每页大小
 * @property {string} [orderField] - 排序字段
 * @property {boolean} [isAsc] - 是否升序
 */

/**
 * 员工响应数据
 * @typedef {Object} EmployeeResponseDto
 * @property {string} employeeId - 员工ID
 * @property {string} name - 员工姓名
 * @property {string} phone - 手机号
 * @property {string} email - 邮箱
 * @property {string} avatar - 头像URL
 * @property {string} department - 部门
 * @property {string} position - 职位
 * @property {number} level - 级别
 * @property {string} managerId - 上级管理员ID
 * @property {string} managerName - 上级管理员姓名
 * @property {number} status - 状态
 * @property {string} statusText - 状态文本
 * @property {string} remark - 备注
 * @property {number} userCount - 绑定用户数
 * @property {number} videoCount - 创建视频数
 * @property {number} batchCount - 创建批次数
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 * @property {string} lastLoginTime - 最后登录时间
 */

/**
 * 员工业绩统计
 * @typedef {Object} EmployeePerformanceDto
 * @property {string} employeeId - 员工ID
 * @property {string} name - 员工姓名
 * @property {number} userCount - 绑定用户数
 * @property {number} newUserCount - 新增用户数
 * @property {number} activeUserCount - 活跃用户数
 * @property {number} videoViewCount - 视频观看次数
 * @property {number} answerCount - 答题次数
 * @property {number} correctAnswerCount - 正确答题次数
 * @property {number} rewardAmount - 奖励金额
 * @property {number} transferInCount - 转入用户数
 * @property {number} transferOutCount - 转出用户数
 * @property {string} timeRange - 统计时间范围
 */

/**
 * 分页结果数据
 * @typedef {Object} PagedResultOfEmployeeResponseDto
 * @property {Array<EmployeeResponseDto>} items - 数据列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 创建员工
 * @param {EmployeeCreateDto} data - 员工创建数据
 * @returns {Promise<ApiResult<string>>} 创建结果，返回员工ID
 */
export function createEmployee (data) {
  return request.post('/Employee', data)
}

/**
 * 更新员工信息
 * @param {EmployeeUpdateDto} data - 员工更新数据
 * @returns {Promise<ApiResult<boolean>>} 更新结果
 */
export function updateEmployee (data) {
  return request.put('/Employee', data)
}

/**
 * 删除员工
 * @param {string} employeeId - 员工ID
 * @returns {Promise<ApiResult<boolean>>} 删除结果
 */
export function deleteEmployee (employeeId) {
  return request.delete(`/Employee/${employeeId}`)
}

/**
 * 获取员工详情
 * @param {string} employeeId - 员工ID
 * @returns {Promise<ApiResult<EmployeeResponseDto>>} 员工详情
 */
export function getEmployeeDetail (employeeId) {
  return request.get(`/Employee/${employeeId}`)
}

/**
 * 分页查询员工列表
 * @param {EmployeeQueryDto} params - 查询参数
 * @returns {Promise<ApiResult<PagedResultOfEmployeeResponseDto>>} 分页查询结果
 */
export function queryEmployees (params) {
  return request.get('/Employee', params)
}

/**
 * 获取员工业绩统计
 * @param {string} employeeId - 员工ID
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<EmployeePerformanceDto>>} 员工业绩统计
 */
export function getEmployeePerformance (employeeId, params = {}) {
  return request.get(`/Employee/${employeeId}/performance`, params)
}

/**
 * 获取部门员工列表
 * @param {string} department - 部门名称
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfEmployeeResponseDto>>} 查询结果
 */
export function getEmployeesByDepartment (department, options = {}) {
  return queryEmployees({
    department,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20
  })
}

/**
 * 获取下级员工列表
 * @param {string} managerId - 管理员ID
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfEmployeeResponseDto>>} 查询结果
 */
export function getSubordinates (managerId, options = {}) {
  return queryEmployees({
    managerId,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20
  })
}

/**
 * 根据状态查询员工
 * @param {number} status - 状态
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfEmployeeResponseDto>>} 查询结果
 */
export function getEmployeesByStatus (status, options = {}) {
  return queryEmployees({
    status,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20
  })
}

/**
 * 搜索员工
 * @param {string} keyword - 搜索关键词
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfEmployeeResponseDto>>} 查询结果
 */
export function searchEmployees (keyword, options = {}) {
  return queryEmployees({
    name: keyword,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20
  })
}

/**
 * 批量删除员工
 * @param {Array<string>} employeeIds - 员工ID列表
 * @returns {Promise<Array<ApiResult<boolean>>>} 批量删除结果
 */
export function batchDeleteEmployees (employeeIds) {
  const promises = employeeIds.map(employeeId => deleteEmployee(employeeId))
  return Promise.all(promises)
}

/**
 * 更新员工状态
 * @param {string} employeeId - 员工ID
 * @param {number} status - 新状态
 * @returns {Promise<ApiResult<boolean>>} 更新结果
 */
export function updateEmployeeStatus (employeeId, status) {
  return updateEmployee({
    employeeId,
    status
  })
}

/**
 * 获取员工状态选项
 * @returns {Array<{value: number, label: string, color: string}>} 状态选项
 */
export function getEmployeeStatusOptions () {
  return [
    { value: 1, label: '在职', color: '#52c41a' },
    { value: 2, label: '离职', color: '#f5222d' },
    { value: 3, label: '停职', color: '#faad14' }
  ]
}

/**
 * 获取员工级别选项
 * @returns {Array<{value: number, label: string}>} 级别选项
 */
export function getEmployeeLevelOptions () {
  return [
    { value: 1, label: '初级' },
    { value: 2, label: '中级' },
    { value: 3, label: '高级' },
    { value: 4, label: '专家' },
    { value: 5, label: '管理' }
  ]
}

/**
 * 获取部门选项
 * @returns {Array<{value: string, label: string}>} 部门选项
 */
export function getDepartmentOptions () {
  return [
    { value: '销售部', label: '销售部' },
    { value: '市场部', label: '市场部' },
    { value: '技术部', label: '技术部' },
    { value: '运营部', label: '运营部' },
    { value: '客服部', label: '客服部' },
    { value: '人事部', label: '人事部' },
    { value: '财务部', label: '财务部' }
  ]
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getEmployeeSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'name', label: '员工姓名' },
    { value: 'level', label: '员工级别' },
    { value: 'userCount', label: '绑定用户数' },
    { value: 'lastLoginTime', label: '最后登录时间' }
  ]
}

/**
 * 验证员工数据
 * @param {EmployeeCreateDto|EmployeeUpdateDto} data - 员工数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateEmployeeData (data) {
  const errors = []

  if (!data.employeeId || data.employeeId.trim() === '') {
    errors.push('员工ID不能为空')
  }

  if (!data.name || data.name.trim() === '') {
    errors.push('员工姓名不能为空')
  }

  if (!data.phone || data.phone.trim() === '') {
    errors.push('手机号不能为空')
  } else if (!/^1[3-9]\d{9}$/.test(data.phone)) {
    errors.push('手机号格式不正确')
  }

  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('邮箱格式不正确')
  }

  if (!data.department || data.department.trim() === '') {
    errors.push('部门不能为空')
  }

  if (!data.position || data.position.trim() === '') {
    errors.push('职位不能为空')
  }

  if (data.level === undefined || data.level < 1 || data.level > 5) {
    errors.push('员工级别必须在1-5之间')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 格式化员工数据
 * @param {Array<EmployeeResponseDto>} employees - 员工列表
 * @returns {Array<Object>} 格式化后的员工列表
 */
export function formatEmployeeData (employees) {
  if (!Array.isArray(employees)) return []

  return employees.map(employee => ({
    ...employee,
    // 格式化时间
    createTime: employee.createTime ? new Date(employee.createTime).toLocaleString() : '',
    updateTime: employee.updateTime ? new Date(employee.updateTime).toLocaleString() : '',
    lastLoginTime: employee.lastLoginTime ? new Date(employee.lastLoginTime).toLocaleString() : '从未登录',

    // 处理状态
    statusText: getEmployeeStatusText(employee.status),
    statusColor: getEmployeeStatusColor(employee.status),

    // 处理级别
    levelText: getEmployeeLevelText(employee.level),

    // 处理头像
    avatar: employee.avatar || '/static/images/default-avatar.png',

    // 处理上级管理员
    managerName: employee.managerName || '无'
  }))
}

/**
 * 获取员工状态文本
 * @param {number} status - 状态值
 * @returns {string} 状态文本
 */
export function getEmployeeStatusText (status) {
  const options = getEmployeeStatusOptions()
  const option = options.find(opt => opt.value === status)
  return option ? option.label : '未知状态'
}

/**
 * 获取员工状态颜色
 * @param {number} status - 状态值
 * @returns {string} 状态颜色
 */
export function getEmployeeStatusColor (status) {
  const options = getEmployeeStatusOptions()
  const option = options.find(opt => opt.value === status)
  return option ? option.color : '#d9d9d9'
}

/**
 * 获取员工级别文本
 * @param {number} level - 级别值
 * @returns {string} 级别文本
 */
export function getEmployeeLevelText (level) {
  const options = getEmployeeLevelOptions()
  const option = options.find(opt => opt.value === level)
  return option ? option.label : '未知级别'
}

// 默认导出所有员工管理相关API
export default {
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeeDetail,
  queryEmployees,
  getEmployeePerformance,
  getEmployeesByDepartment,
  getSubordinates,
  getEmployeesByStatus,
  searchEmployees,
  batchDeleteEmployees,
  updateEmployeeStatus,
  getEmployeeStatusOptions,
  getEmployeeLevelOptions,
  getDepartmentOptions,
  getEmployeeSortFieldOptions,
  validateEmployeeData,
  formatEmployeeData,
  getEmployeeStatusText,
  getEmployeeStatusColor,
  getEmployeeLevelText
}
