/**
 * 测试管理员和微信用户认证系统分离
 * 验证两套系统的存储键名和数据不会相互干扰
 */

import adminAuthService from './adminAuthService.js'
import wechatUserService from './wechatUserService.js'

/**
 * 测试两套认证系统的独立性
 */
export function testAuthSeparation() {
  console.log('=== 开始测试认证系统分离 ===')
  
  // 清理所有存储
  console.log('1. 清理所有存储...')
  clearAllStorage()
  
  // 测试管理员系统
  console.log('2. 测试管理员系统...')
  testAdminAuthSystem()
  
  // 测试微信用户系统
  console.log('3. 测试微信用户系统...')
  testWechatUserSystem()
  
  // 验证两套系统独立性
  console.log('4. 验证系统独立性...')
  verifySystemSeparation()
  
  console.log('=== 认证系统分离测试完成 ===')
}

/**
 * 清理所有存储
 */
function clearAllStorage() {
  try {
    // 清理管理员存储
    adminAuthService.logout()
    
    // 清理微信用户存储
    wechatUserService.logout()
    
    // 清理旧的混合存储键
    uni.removeStorageSync('loginInfo')
    uni.removeStorageSync('userPermissions')
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('userToken')
    
    console.log('✓ 存储清理完成')
  } catch (error) {
    console.error('✗ 存储清理失败:', error)
  }
}

/**
 * 测试管理员认证系统
 */
function testAdminAuthSystem() {
  try {
    // 模拟管理员登录信息
    const mockAdminInfo = {
      username: 'admin_test',
      userId: 'admin_001',
      nickName: '测试管理员',
      userType: 'admin',
      userTypeCode: 1,
      accessToken: 'admin_token_123',
      permissions: ['*'],
      loginTime: new Date().getTime()
    }
    
    // 保存管理员信息
    adminAuthService.saveLoginInfo(mockAdminInfo)
    
    // 验证管理员信息
    const retrievedAdminInfo = adminAuthService.getLoginInfo()
    console.log('管理员信息:', retrievedAdminInfo)
    
    // 验证管理员状态
    const isAdminLoggedIn = adminAuthService.isLoggedIn()
    const adminUserType = adminAuthService.getUserType()
    const adminUserId = adminAuthService.getUserId()
    
    console.log(`✓ 管理员登录状态: ${isAdminLoggedIn}`)
    console.log(`✓ 管理员用户类型: ${adminUserType}`)
    console.log(`✓ 管理员用户ID: ${adminUserId}`)
    
    // 验证权限
    const hasPermission = adminAuthService.hasPermission('user:manage')
    console.log(`✓ 管理员权限检查: ${hasPermission}`)
    
  } catch (error) {
    console.error('✗ 管理员系统测试失败:', error)
  }
}

/**
 * 测试微信用户系统
 */
function testWechatUserSystem() {
  try {
    // 初始化模拟微信用户
    const mockResult = wechatUserService.initMockWechatUser()
    
    // 验证微信用户信息
    const wechatUserInfo = wechatUserService.getUserInfo()
    const wechatUserToken = wechatUserService.getUserToken()
    
    console.log('微信用户信息:', wechatUserInfo)
    console.log('微信用户Token:', wechatUserToken)
    
    // 验证微信用户状态
    const isWechatLoggedIn = wechatUserService.isLoggedIn()
    const wechatUserId = wechatUserService.getUserId()
    const wechatNickname = wechatUserService.getNickname()
    
    console.log(`✓ 微信用户登录状态: ${isWechatLoggedIn}`)
    console.log(`✓ 微信用户ID: ${wechatUserId}`)
    console.log(`✓ 微信用户昵称: ${wechatNickname}`)
    
  } catch (error) {
    console.error('✗ 微信用户系统测试失败:', error)
  }
}

/**
 * 验证系统独立性
 */
function verifySystemSeparation() {
  try {
    // 获取两套系统的信息
    const adminInfo = adminAuthService.getLoginInfo()
    const wechatInfo = wechatUserService.getUserInfo()
    
    console.log('=== 系统独立性验证 ===')
    
    // 验证存储键名不同
    console.log('管理员存储键: adminLoginInfo, adminUserPermissions')
    console.log('微信用户存储键: wechatUserInfo, wechatUserToken')
    
    // 验证数据结构不同
    console.log('管理员数据结构:', Object.keys(adminInfo || {}))
    console.log('微信用户数据结构:', Object.keys(wechatInfo || {}))
    
    // 验证用户ID不同
    const adminUserId = adminAuthService.getUserId()
    const wechatUserId = wechatUserService.getUserId()
    
    console.log(`管理员用户ID: ${adminUserId}`)
    console.log(`微信用户ID: ${wechatUserId}`)
    
    if (adminUserId !== wechatUserId) {
      console.log('✓ 用户ID独立性验证通过')
    } else {
      console.log('✗ 用户ID独立性验证失败')
    }
    
    // 验证用户类型不同
    const adminUserType = adminAuthService.getUserType()
    const wechatDisplayInfo = wechatUserService.getDisplayInfo()
    
    console.log(`管理员用户类型: ${adminUserType}`)
    console.log(`微信用户类型: ${wechatDisplayInfo.userType}`)
    
    if (adminUserType !== wechatDisplayInfo.userType) {
      console.log('✓ 用户类型独立性验证通过')
    } else {
      console.log('✗ 用户类型独立性验证失败')
    }
    
    // 验证登录状态独立
    const adminLoggedIn = adminAuthService.isLoggedIn()
    const wechatLoggedIn = wechatUserService.isLoggedIn()
    
    console.log(`管理员登录状态: ${adminLoggedIn}`)
    console.log(`微信用户登录状态: ${wechatLoggedIn}`)
    
    if (adminLoggedIn && wechatLoggedIn) {
      console.log('✓ 两套系统可以同时登录，独立性验证通过')
    } else {
      console.log('✗ 系统登录状态验证失败')
    }
    
    // 测试单独登出
    console.log('=== 测试单独登出 ===')
    
    // 登出管理员
    adminAuthService.logout()
    const adminLoggedInAfterLogout = adminAuthService.isLoggedIn()
    const wechatLoggedInAfterAdminLogout = wechatUserService.isLoggedIn()
    
    console.log(`管理员登出后 - 管理员登录状态: ${adminLoggedInAfterLogout}`)
    console.log(`管理员登出后 - 微信用户登录状态: ${wechatLoggedInAfterAdminLogout}`)
    
    if (!adminLoggedInAfterLogout && wechatLoggedInAfterAdminLogout) {
      console.log('✓ 管理员登出不影响微信用户，独立性验证通过')
    } else {
      console.log('✗ 管理员登出影响了微信用户，独立性验证失败')
    }
    
    // 登出微信用户
    wechatUserService.logout()
    const wechatLoggedInAfterLogout = wechatUserService.isLoggedIn()
    
    console.log(`微信用户登出后 - 微信用户登录状态: ${wechatLoggedInAfterLogout}`)
    
    if (!wechatLoggedInAfterLogout) {
      console.log('✓ 微信用户登出成功')
    } else {
      console.log('✗ 微信用户登出失败')
    }
    
    console.log('✓ 系统独立性验证完成')
    
  } catch (error) {
    console.error('✗ 系统独立性验证失败:', error)
  }
}

/**
 * 检查存储键名冲突
 */
export function checkStorageKeyConflicts() {
  console.log('=== 检查存储键名冲突 ===')
  
  const adminKeys = ['adminLoginInfo', 'adminUserPermissions']
  const wechatKeys = ['wechatUserInfo', 'wechatUserToken']
  const oldKeys = ['loginInfo', 'userPermissions', 'userInfo', 'userToken']
  
  console.log('管理员存储键:', adminKeys)
  console.log('微信用户存储键:', wechatKeys)
  console.log('旧的混合存储键:', oldKeys)
  
  // 检查是否有重复
  const allKeys = [...adminKeys, ...wechatKeys]
  const uniqueKeys = [...new Set(allKeys)]
  
  if (allKeys.length === uniqueKeys.length) {
    console.log('✓ 存储键名无冲突')
  } else {
    console.log('✗ 存储键名存在冲突')
  }
  
  // 检查是否还在使用旧键名
  oldKeys.forEach(key => {
    try {
      const value = uni.getStorageSync(key)
      if (value) {
        console.log(`⚠️ 警告: 仍在使用旧存储键 "${key}":`, value)
      }
    } catch (error) {
      // 忽略错误
    }
  })
}

export default {
  testAuthSeparation,
  checkStorageKeyConflicts
}
