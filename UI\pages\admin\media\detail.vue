<template>
	<view class="page-container">
		<!-- 视频预览区域 -->
		<view class="video-preview-section">
			<!-- 压缩中状态显示遮罩 -->
			<view v-if="videoInfo.status === 'compressing'" class="video-processing-overlay">
				<view class="processing-content">
					<u-loading-icon mode="circle" size="60" color="#409eff"></u-loading-icon>
					<text class="processing-text">视频压缩中，暂时无法观看</text>
					<text class="processing-desc">请稍后再试</text>
				</view>
			</view>
			<!-- 处理失败状态显示遮罩 -->
			<view v-else-if="videoInfo.status === 'failed'" class="video-failed-overlay">
				<view class="failed-content">
					<text class="failed-icon">⚠️</text>
					<text class="failed-text">视频处理失败</text>
					<text class="failed-desc">请重新上传视频</text>
				</view>
			</view>
			<!-- 正常视频播放器 -->
			<video v-else :src="videoInfo.url" :poster="videoInfo.cover" class="video-player" controls></video>
			<u-tag :text="getStatusText(videoInfo)" :type="getStatusType(videoInfo)" class="video-status-tag"></u-tag>
		</view>

		<!-- 视频信息区域 -->
		<view class="content-section">
			<u-card :title="videoInfo.title" :padding="10" margin="20rpx">
				<template #body>
					<view class="video-info">
						<view class="info-item">
							<text class="info-label">上传时间:</text>
							<text class="info-value">{{ formatDate(videoInfo.uploadTime) }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">上传者:</text>
							<text class="info-value">{{ videoInfo.uploader }}</text>
						</view>
					</view>

					<view class="video-desc" v-if="videoInfo.description">
						<text>{{ videoInfo.description }}</text>
					</view>

					<!-- 操作按钮区域 -->
					<view class="operation-section">
						<u-button text="操作" type="primary" @click="showActionSheet"
							:custom-style="{ width: '160rpx', height: '60rpx', fontSize: '28rpx' }"></u-button>
					</view>
				</template>
			</u-card>
		</view>

		<!-- 相关问题区域 -->
		<view class="content-section" v-if="quizzes.length > 0">
			<u-card :padding="10" margin="20rpx">
				<template #head>
					<view class="quiz-card-header">
						<text class="quiz-card-title">相关问题</text>
						<u-tag :text="rewardText" type="error" icon="gift" size="small"></u-tag>
					</view>
				</template>
				<template #body>
					<view class="quiz-item" v-for="(quiz, index) in quizzes" :key="quiz.id">
						<view class="quiz-header">
							<text class="quiz-number">问题 {{ index + 1 }}</text>
							<u-tag text="选择题" type="primary" size="mini"></u-tag>
						</view>
						<view class="quiz-content">
							<text class="quiz-question">{{ quiz.question }}</text>
						</view>
						<view class="quiz-options">
							<view class="option-item" v-for="option in quiz.options" :key="option.id">
								<text class="option-text"
									:class="{ 'correct-option': isCorrectOption(quiz, option.id) }">
									{{ option.id }}. {{ option.text }}
									<text v-if="isCorrectOption(quiz, option.id)" class="correct-option-badge">✓</text>
								</text>
							</view>
						</view>
						<!-- 显示正确答案 -->
						<view class="quiz-correct-answer">
							<text class="correct-answer-label">正确答案:</text>
							<text class="correct-answer-value">{{ quiz.correctAnswer }}</text>
						</view>
						<!-- 显示答案解释 -->
						<view class="quiz-explanation" v-if="quiz.explanation">
							<text class="explanation-label">解释:</text>
							<text class="explanation-content">{{ quiz.explanation }}</text>
						</view>
					</view>
				</template>
			</u-card>
		</view>

		<!-- 删除确认弹窗 -->
		<u-popup :show="showDeletePopup" mode="center" :closeOnClickOverlay="true" @close="cancelDelete">
			<view class="delete-popup-container">
				<view class="delete-popup-header">
					<text class="delete-popup-title">确认删除</text>
				</view>
				<view class="delete-popup-body">
					<view class="delete-popup-icon">⚠️</view>
					<text class="delete-popup-message">确定要删除这个视频吗？</text>
					<text class="delete-popup-desc">相关的问题和奖励也会被删除，此操作不可恢复。</text>
				</view>
				<view class="delete-popup-footer">
					<u-button text="取消" type="default" @click="cancelDelete"
						:custom-style="{ marginRight: '20rpx' }"></u-button>
					<u-button text="确认删除" type="error" @click="handleDelete"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 操作选择弹窗 -->
		<u-action-sheet :show="showActionPopup" :actions="actionList" @close="closeActionSheet"
			@select="handleActionSelect" title="选择操作" cancelText="取消"></u-action-sheet>
	</view>
</template>

<script>
import { getVideoDetail, deleteVideo } from "@/api/video.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
	mixins: [mediaCommonMixin],
	data () {
		return {
			videoId: 0,
			videoInfo: {},
			quizzes: [],
			showDeletePopup: false,
			showActionPopup: false,
			actionList: [
				{
					name: '编辑视频',
					icon: 'edit-pen',
					color: '#3c9cff'
				},
				{
					name: '创建批次',
					icon: 'plus-circle',
					color: '#5ac725'
				},
				{
					name: '删除视频',
					icon: 'trash',
					color: '#f56c6c'
				}
			]
		}
	},
	computed: {
		quizCount () {
			return this.quizzes.length;
		},
		totalReward () {
			return this.videoInfo.rewardAmount || 0;
		},
		rewardText () {
			return '奖励: ' + this.totalReward + '元';
		}
	},
	onLoad (options) {
		if (options.id) {
			this.videoId = parseInt(options.id);
			this.loadVideoInfo();
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			});
			setTimeout(() => {
				this.goBack();
			}, 1500);
		}
	},
	onShow () {
		if (this.videoId) {
			this.loadVideoInfo();
		}
	},
	methods: {
		async loadVideoInfo () {
			try {
				uni.showLoading({
					title: "加载中...",
				});

				const response = await getVideoDetail(this.videoId);

				if (response.success && response.data) {
					const video = response.data;
					this.videoInfo = {
						id: video.id,
						title: video.title,
						cover: this.buildCompleteFileUrl(video.coverUrl) || '/assets/images/video-cover.jpg',
						url: this.buildCompleteFileUrl(video.videoUrl) || 'https://www.runoob.com/try/demo_source/mov_bbb.mp4',
						duration: this.formatDuration(video.duration),
						uploadTime: video.createTime,
						uploader: video.creatorName || '未知',
						uploaderId: video.creatorId || '',
						views: video.viewCount || 0,
						likes: video.likeCount || 0,
						description: video.description || '',
						status: this.mapVideoStatus(video.status),
						rewardAmount: video.rewardAmount || 0
					};

					this.processQuestions(video.questions || []);
				} else {
					throw new Error(response.msg || '获取视频详情失败');
				}

				uni.hideLoading();
			} catch (error) {
				console.error('加载视频详情失败:', error);
				uni.hideLoading();

				uni.showToast({
					title: '加载视频详情失败',
					icon: 'none'
				});
			}
		},

		mapVideoStatus (apiStatus) {
			const statusMap = {
				0: 'offline',      // 下架
				1: 'online',       // 上架
				2: 'failed',       // 失败
				3: 'compressing'   // 压缩中
			};
			return statusMap[apiStatus] || 'offline';
		},





		processQuestions (questions) {
			try {
				if (questions && questions.length > 0) {
					this.quizzes = questions.map((q, index) => {
						const options = (q.options || []).map((option, optIndex) => ({
							id: String.fromCharCode(65 + optIndex),
							text: option.optionText || option.text || `选项${optIndex + 1}`
						}));

						let correctAnswer = 'A';
						if (q.options && q.options.length > 0) {
							const correctOption = q.options.find(opt => opt.isCorrect);
							if (correctOption) {
								const correctIndex = q.options.indexOf(correctOption);
								correctAnswer = String.fromCharCode(65 + correctIndex);
							}
						}

						return {
							id: q.id || (index + 1),
							question: q.questionText || q.question || `问题${index + 1}`,
							options: options,
							correctAnswer: correctAnswer,
							explanation: q.explanation || ''
						};
					});
				} else {
					this.quizzes = [];
				}
			} catch (error) {
				console.error('处理问题数据失败:', error);
				this.quizzes = [];
			}
		},

		editVideo () {
			uni.navigateTo({
				url: `/pages/admin/media/upload?id=${this.videoId}`
			});
		},

		goBack () {
			uni.navigateBack();
		},

		showDeleteConfirm () {
			this.showDeletePopup = true;
		},

		cancelDelete () {
			this.showDeletePopup = false;
		},

		async handleDelete () {
			this.showDeletePopup = false;

			try {
				// 显示加载提示
				uni.showLoading({
					title: '删除中...'
				});

				// 调用删除API
				const response = await deleteVideo(this.videoId);

				uni.hideLoading();

				if (response.success) {
					// 删除成功
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});

					// 使用事件机制确保页面返回后刷新
					setTimeout(() => {
						// 发送全局事件通知列表页面刷新
						uni.$emit('videoDeleted', { videoId: this.videoId });

						// 返回上一页
						uni.navigateBack();
					}, 1500);
				} else {
					// 删除失败
					uni.showToast({
						title: response.msg || '删除失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('删除视频失败:', error);
				uni.hideLoading();

				uni.showToast({
					title: '删除失败，请重试',
					icon: 'none'
				});
			}
		},

		createBatch () {
			uni.navigateTo({
				url: `/pages/admin/media/publish?id=${this.videoId}`
			});
		},

		isCorrectOption (quiz, optionId) {
			if (typeof quiz.correctAnswer === 'string') {
				return quiz.correctAnswer === optionId;
			} else if (Array.isArray(quiz.correctAnswer)) {
				return quiz.correctAnswer.includes(optionId);
			}
			return false;
		},

		// 显示操作选择弹窗
		showActionSheet () {
			this.showActionPopup = true;
		},

		// 关闭操作选择弹窗
		closeActionSheet () {
			this.showActionPopup = false;
		},

		// 处理操作选择
		handleActionSelect (item) {
			this.showActionPopup = false;

			switch (item.name) {
				case '编辑视频':
					this.editVideo();
					break;
				case '创建批次':
					this.createBatch();
					break;
				case '删除视频':
					this.showDeleteConfirm();
					break;
				default:
					break;
			}
		}
	}
}
</script>

<style lang="scss">
@import '@/styles/variables.scss';

.page-container {
	background-color: $bg-secondary;
	min-height: 100vh;
	padding-bottom: $spacing-base;
}

.video-preview-section {
	width: 100%;
	height: 380rpx;
	background-color: #000;
	position: relative;
}

.video-player {
	width: 100%;
	height: 100%;
}

.video-status-tag {
	position: absolute;
	top: $spacing-base;
	right: $spacing-base;
	z-index: 10;
}

.video-processing-overlay,
.video-failed-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 5;
}

.processing-content,
.failed-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.processing-text,
.failed-text {
	color: #fff;
	font-size: $font-size-lg;
	font-weight: $font-weight-medium;
	margin-top: $spacing-base;
	margin-bottom: $spacing-sm;
}

.processing-desc,
.failed-desc {
	color: rgba(255, 255, 255, 0.7);
	font-size: $font-size-base;
}

.failed-icon {
	font-size: 60rpx;
	margin-bottom: $spacing-sm;
}

.content-section {
	margin-bottom: $spacing-sm;
}

.video-info {
	margin-bottom: $spacing-sm;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	width: 140rpx;
	font-size: $font-size-sm;
	color: $text-secondary;
}

.info-value {
	font-size: $font-size-sm;
	color: $text-primary;
}

.video-desc {
	margin-top: $spacing-base;
	padding-top: $spacing-base;
	border-top: 1rpx solid $border-secondary;
	font-size: $font-size-base;
	color: $text-primary;
	line-height: $line-height-relaxed;
}

.quiz-item {
	border-bottom: 1rpx solid $border-secondary;
	padding-bottom: $spacing-base;
	margin-bottom: $spacing-base;
}

.quiz-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.quiz-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-sm;
}

.quiz-number {
	font-size: $font-size-md;
	color: $text-primary;
	font-weight: $font-weight-semibold;
}

.quiz-content {
	font-size: $font-size-md;
	color: $text-primary;
	line-height: $line-height-relaxed;
	margin-bottom: $spacing-lg;
	font-weight: $font-weight-medium;
}

.quiz-options {
	display: flex;
	flex-direction: column;
	gap: $spacing-base;
	margin-bottom: $spacing-base;
}

.option-item {
	background-color: $bg-secondary;
	border-radius: $border-radius-base;
	border: 1rpx solid $border-secondary;
	padding: $spacing-sm;
}

.option-text {
	font-size: $font-size-base;
	color: $text-primary;
}

.correct-option {
	color: $success-color;
	font-weight: $font-weight-semibold;
}

.correct-option-badge {
	display: inline-block;
	margin-left: 10rpx;
	color: $text-white;
	background-color: $success-color;
	border-radius: 50%;
	width: 36rpx;
	height: 36rpx;
	line-height: 36rpx;
	text-align: center;
	font-size: $font-size-sm;
}

.quiz-correct-answer {
	margin-bottom: $spacing-base;
	font-size: $font-size-sm;
	color: $text-secondary;
}

.correct-answer-label {
	font-weight: $font-weight-semibold;
}

.correct-answer-value {
	margin-left: $spacing-sm;
}

.quiz-explanation {
	font-size: $font-size-sm;
	color: $text-secondary;
}

.explanation-label {
	font-weight: $font-weight-semibold;
	margin-right: 8rpx;
}

.explanation-content {
	margin-left: $spacing-sm;
}

.delete-popup-container {
	background-color: $bg-primary;
	border-radius: $border-radius-lg;
	width: 600rpx;
	max-width: 90vw;
	overflow: hidden;
	box-shadow: $shadow-lg;
}

.delete-popup-header {
	padding: $spacing-lg $spacing-lg $spacing-base $spacing-lg;
	border-bottom: 1rpx solid $border-secondary;
}

.delete-popup-title {
	font-size: $font-size-lg;
	font-weight: $font-weight-semibold;
	color: $text-primary;
	text-align: center;
}

.delete-popup-body {
	padding: $spacing-lg;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.delete-popup-icon {
	font-size: 80rpx;
	margin-bottom: $spacing-base;
}

.delete-popup-message {
	font-size: $font-size-md;
	color: $text-primary;
	margin-bottom: $spacing-sm;
	font-weight: $font-weight-medium;
}

.delete-popup-desc {
	font-size: $font-size-sm;
	color: $text-tertiary;
	line-height: $line-height-relaxed;
}

.delete-popup-footer {
	padding: $spacing-base $spacing-lg $spacing-lg $spacing-lg;
	display: flex;
	gap: $spacing-base;
	justify-content: center;
	border-top: 1rpx solid $border-secondary;
	background-color: $bg-secondary;
}

.operation-section {
	margin-top: $spacing-base;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: -6rpx;
	right: 10rpx;
}

.quiz-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.quiz-card-title {
	font-size: $font-size-lg;
	font-weight: $font-weight-semibold;
	color: $text-primary;
}
</style>