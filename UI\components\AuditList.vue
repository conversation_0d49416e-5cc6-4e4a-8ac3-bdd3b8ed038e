<template>
    <view class="audit-container">
        <!-- 加载状态 -->
        <u-loadmore v-if="loading" status="loading" loadingText="加载中..." />

        <!-- 列表 -->
        <view class="audit-list" v-else>
            <!-- 审核项目卡片 -->
            <u-card v-for="(item, index) in listData" :key="item.id" :padding="0" margin="0 0 20rpx 0">
                <!-- 卡片头部 -->
                <template #head>
                    <view class="card-header">
                        <view class="user-basic-info">
                            <u-avatar :src="item.avatar || '/static/images/avatar-placeholder.png'"
                                size="40"></u-avatar>
                            <view class="user-info">
                                <view class="user-name">{{ item.username || item.name || '未知用户' }}</view>
                                <u-tag :text="getRoleText(item.role || auditType)" type="info" size="mini"
                                    :plain="true"></u-tag>
                            </view>
                        </view>
                        <u-tag :text="getStatusText(item.status)" :type="getStatusType(item.status)"
                            size="mini"></u-tag>
                    </view>
                </template>

                <!-- 卡片内容 -->
                <template #body>
                    <view class="card-body">
                        <!-- 动态渲染额外信息字段 -->
                        <u-cell-group :border="false" v-if="extraFields.length > 0 || item.applyTime || item.auditTime">
                            <u-cell v-for="(field, fieldIndex) in extraFields" :key="fieldIndex" :title="field.label"
                                :value="getFieldValue(item, field)" :border="false" size="small" />
                            <u-cell title="申请时间" :value="formatDate(item.applyTime)" :border="false" size="small" />
                            <u-cell v-if="item.status !== 'pending'" title="审核时间" :value="formatDate(item.auditTime)"
                                :border="false" size="small" />
                        </u-cell-group>
                    </view>
                </template>

                <!-- 卡片底部操作按钮 -->
                <template #foot>
                    <view class="card-footer">
                        <template v-if="item.status === 'pending'">
                            <u-button text="拒绝" type="info" :plain="true" size="small"
                                @click="showConfirmModal(item, 'reject')">
                                <template #icon>
                                    <u-icon name="close" size="14"></u-icon>
                                </template>
                            </u-button>
                            <u-button text="通过" type="primary" size="small" @click="showConfirmModal(item, 'approve')">
                                <template #icon>
                                    <u-icon name="checkmark" size="14"></u-icon>
                                </template>
                            </u-button>
                        </template>
                        <template v-else>
                            <u-button text="查看详情" type="info" :plain="true" size="small" @click="viewDetail(item)">
                                <template #icon>
                                    <u-icon name="eye" size="14"></u-icon>
                                </template>
                            </u-button>
                        </template>
                    </view>
                </template>
            </u-card>

            <!-- 空状态 -->
            <u-empty v-if="listData.length === 0" mode="data" :text="`暂无${typeLabel}申请`"></u-empty>
        </view>

        <!-- 确认弹窗 -->
        <u-modal v-model="showConfirmPopup" :title="`确认${confirmType === 'approve' ? '通过' : '拒绝'}`"
            :showCancelButton="true" @confirm="handleConfirm" @cancel="cancelConfirm">
            <view class="modal-content">
                <text class="modal-text">确定要{{ confirmType === 'approve' ? '通过' : '拒绝' }}该申请吗？</text>
                <u-input v-if="confirmType === 'reject'" v-model="rejectReason" placeholder="请输入拒绝原因（可选）"
                    type="textarea" :autoHeight="true" maxlength="200" style="margin-top: 20rpx;"></u-input>
            </view>
        </u-modal>
    </view>
</template>

<script>
import { formatDateTime } from "@/utils/format.js";

export default {
    name: 'AuditList',
    props: {
        // 审核类型: employee, agent, user
        auditType: {
            type: String,
            required: true
        },
        // 审核列表数据
        listData: {
            type: Array,
            required: true
        },
        // 额外展示的信息字段
        extraFields: {
            type: Array,
            default: () => []
        },
        // 类型标签（用于显示）
        typeLabel: {
            type: String,
            required: true
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            showConfirmPopup: false,
            confirmType: '',
            currentItem: null,
            rejectReason: ''
        }
    },
    methods: {
        getStatusText (status) {
            if (status === 'approved') return '已通过';
            if (status === 'rejected') return '已拒绝';
            return '待审核';
        },
        getStatusType (status) {
            if (status === 'approved') return 'success';
            if (status === 'rejected') return 'error';
            return 'warning';
        },
        formatDate (dateString) {
            return formatDateTime(dateString);
        },
        showConfirmModal (item, type) {
            this.currentItem = item;
            this.confirmType = type;
            this.rejectReason = '';
            this.showConfirmPopup = true;
        },
        cancelConfirm () {
            this.showConfirmPopup = false;
            this.currentItem = null;
            this.confirmType = '';
            this.rejectReason = '';
        },
        handleConfirm () {
            if (this.confirmType === 'approve') {
                this.$emit('approve', this.currentItem);
            } else {
                this.$emit('reject', { item: this.currentItem, reason: this.rejectReason });
            }
            this.showConfirmPopup = false;
            this.currentItem = null;
            this.confirmType = '';
            this.rejectReason = '';
        },
        viewDetail (item) {
            this.$emit('view-detail', item);
        },
        getRoleText (role) {
            const roleMap = {
                'employee': '员工',
                'agent': '代理',
                'user': '用户'
            };
            return roleMap[role] || '未知';
        },
        getFieldValue (item, field) {
            if (field.path && typeof field.path === 'string') {
                const paths = field.path.split('.');
                let value = item;
                for (const path of paths) {
                    if (value === undefined || value === null) return field.defaultValue || '无';
                    value = value[path];
                }
                return value || field.defaultValue || '无';
            } else {
                return field.defaultValue || '无';
            }
        }
    }
}
</script>

<style lang="scss">
.audit-container {
    width: 100%;
    padding: 20rpx;
}

.audit-list {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
}

.user-basic-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.user-name {
    font-size: 30rpx;
    font-weight: 500;
    color: #303133;
}

.card-body {
    padding: 0 20rpx;
}

.card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
    padding: 20rpx;
}

.modal-content {
    padding: 20rpx 0;
}

.modal-text {
    font-size: 28rpx;
    color: #303133;
    line-height: 1.5;
}
</style>