/**
 * 应用配置工具类
 * 用于获取外部配置文件中的配置项
 */

/**
 * 获取UI项目访问地址
 * @returns {string} UI项目的访问地址
 */
export function getUIProjectUrl () {
  // 检查全局配置是否存在
  if (!window.APP_CONFIG) {
    throw new Error('配置文件未加载！请检查 /static/config/app-config.js 文件是否存在且正确加载。');
  }

  // 检查UIProjectUrl配置是否存在
  if (!window.APP_CONFIG.UIProjectUrl) {
    throw new Error('UIProjectUrl 配置项未找到！请检查 app-config.js 文件中的 UIProjectUrl 配置。');
  }

  return window.APP_CONFIG.UIProjectUrl;
}

/**
 * 获取应用配置项
 * @param {string} key 配置项键名
 * @param {any} defaultValue 默认值
 * @returns {any} 配置项值
 */
export function getAppConfig (key, defaultValue = null) {
  if (window.APP_CONFIG && window.APP_CONFIG[key] !== undefined) {
    return window.APP_CONFIG[key];
  }

  if (defaultValue !== null) {
    console.warn(`APP_CONFIG.${key} not found, using default value:`, defaultValue);
    return defaultValue;
  }

  console.warn(`APP_CONFIG.${key} not found and no default value provided`);
  return null;
}

/**
 * 设置应用配置项
 * @param {string} key 配置项键名
 * @param {any} value 配置项值
 */
export function setAppConfig (key, value) {
  if (window.setAppConfig) {
    window.setAppConfig(key, value);
  } else {
    console.error('setAppConfig function not available');
  }
}

/**
 * 检查配置是否已加载
 * @returns {boolean} 配置是否已加载
 */
export function isConfigLoaded () {
  return !!(window.APP_CONFIG);
}

/**
 * 等待配置加载完成
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否加载成功
 */
export function waitForConfig (timeout = 5000) {
  return new Promise((resolve) => {
    if (isConfigLoaded()) {
      resolve(true);
      return;
    }

    let attempts = 0;
    const maxAttempts = timeout / 100;

    const checkConfig = () => {
      attempts++;
      if (isConfigLoaded()) {
        resolve(true);
      } else if (attempts >= maxAttempts) {
        console.warn('Config loading timeout');
        resolve(false);
      } else {
        setTimeout(checkConfig, 100);
      }
    };

    checkConfig();
  });
}
