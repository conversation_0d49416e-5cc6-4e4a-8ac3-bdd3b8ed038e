<template>
  <view class="container">
    <!-- 悬浮头部区域 -->
    <view class="fixed-header">
      <view class="header-content">
        <!-- 用户状态切换选项卡 -->
        <u-tabs :list="tabList" :current="currentTab" @change="switchTab" lineColor="#186BFF" activeColor="#186BFF"
          inactiveColor="#909399" />

        <!-- 用户查询 -->
        <view class="search-container">
          <u-search placeholder="搜索用户名..." v-model="searchKeyword" @search="onSearch" @clear="clearSearch"
            :showAction="false" />
        </view>

        <!-- 筛选选项卡 -->
        <u-subsection :list="filterList" :current="currentFilter" @change="setFilter" activeColor="#186BFF" />
      </view>
    </view>

    <!-- 用户列表容器 -->
    <view class="list-container">
      <!-- 加载状态 -->
      <u-loadmore v-if="loading" status="loading" loadingText="加载中..." />

      <!-- 用户列表 -->
      <view class="user-list" v-else-if="filteredUsers.length > 0">
        <view class="user-card-wrapper" v-for="user in filteredUsers" :key="user.id" @tap="viewUserDetail(user)">
          <UserInfoCard :userInfo="formatUserInfo(user)" :showDetailBtn="false" :showFooterBtns="true"
            :showUsersBtn="false" :showEmployeesBtn="false" :showAccountBtn="true" @view-detail="viewUserDetail"
            @disableAccount="banUser" @enableAccount="unbanUser" />
        </view>
      </view>

      <!-- 空状态 -->
      <u-empty v-else mode="data" text="暂无用户数据" />
    </view>

  </view>
</template>

<script>
import UserInfoCard from "../../../components/UserInfoCard.vue";
import { queryVideoUsers, formatUserData } from "../../../api/video-user.js";

export default {
  components: {
    UserInfoCard
  },
  data () {
    return {
      users: [],
      bannedUsers: [],
      loading: false,
      currentTab: 0, // 当前选项卡索引
      currentFilter: 0, // 当前筛选索引
      searchKeyword: "", // 搜索关键词

      // uview-plus 组件数据
      tabList: [
        { name: '正常用户' },
        { name: '禁用用户' }
      ],
      filterList: [
        { name: '全部' },
        { name: '今日新增' },
        { name: '活跃用户' },
        { name: '不活跃' }
      ]
    };
  },
  computed: {
    filteredUsers () {
      // 根据当前选项卡选择用户列表
      let userList = this.currentTab === 0 ? this.users : this.bannedUsers;

      // 根据搜索关键词过滤
      if (this.searchKeyword.trim()) {
        userList = userList.filter((user) =>
          (user.username || user.nickname || '').toLowerCase().includes(this.searchKeyword.toLowerCase().trim())
        );
      }

      // 根据筛选条件过滤
      switch (this.currentFilter) {
        case 1: // 今日新增
          const today = new Date().toISOString().split("T")[0];
          userList = userList.filter((user) => {
            const registerDate = new Date(user.createTime || user.registerTime)
              .toISOString()
              .split("T")[0];
            return registerDate === today;
          });
          break;
        case 2: // 活跃用户 (有观看视频或完成测验的用户)
          userList = userList.filter((user) =>
            (user.watchedVideos || 0) > 0 || (user.completedQuizzes || 0) > 0
          );
          break;
        case 3: // 不活跃用户 (没有观看视频且没有完成测验的用户)
          userList = userList.filter((user) =>
            (user.watchedVideos || 0) === 0 && (user.completedQuizzes || 0) === 0
          );
          break;
        default: // 全部用户
          break;
      }

      return userList;
    },

    normalUsersCount () {
      return this.users.length;
    },

    bannedUsersCount () {
      return this.bannedUsers.length;
    }
  },
  onLoad () {
    // 加载所有用户数据
    this.loadAllUsers();
  },
  methods: {
    async loadAllUsers () {
      this.loading = true;
      try {
        // 获取所有用户数据
        const response = await queryVideoUsers({
          PageIndex: 1,
          PageSize: 1000 // 获取所有用户
        });

        if (response.success && response.data) {
          const allUsers = response.data.items || response.data || [];

          // 格式化用户数据并分类
          const formattedUsers = allUsers.map(user => formatUserData(user));

          // 根据用户状态分类（假设status为1是正常，0是禁用）
          this.users = formattedUsers.filter(user => user.status !== 0);
          this.bannedUsers = formattedUsers.filter(user => user.status === 0);

          // 更新选项卡标题显示数量
          this.updateTabTitles();
        }
      } catch (error) {
        console.error('加载用户数据失败:', error);
        uni.showToast({
          title: '加载用户数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    updateTabTitles () {
      // 更新选项卡标题显示用户数量
      this.tabList = [
        { name: `正常用户(${this.normalUsersCount})` },
        { name: `禁用用户(${this.bannedUsersCount})` }
      ];
    },

    formatUserInfo (user) {
      // 将用户数据格式化为UserInfoCard组件需要的格式
      return {
        ...user,
        type: 'user', // 设置用户类型
        disabled: this.currentTab === 1, // 根据当前选项卡设置禁用状态
        username: user.username || user.nickname,
        phone: user.phone || user.mobile || '未设置',
        avatar: user.avatar || '/assets/images/avatar-placeholder.png'
      };
    },

    switchTab (item, index) {
      this.currentTab = index;
      this.currentFilter = 0; // 重置筛选条件

      // 显示切换提示
      uni.showToast({
        title: index === 0 ? '切换到正常用户' : '切换到禁用用户',
        icon: 'none',
        duration: 1000
      });
    },

    setFilter (index) {
      this.currentFilter = index;
    },

    onSearch () {
      // 搜索时的处理，当前直接通过计算属性实时过滤
      // 可以在这里添加搜索统计等逻辑
    },

    clearSearch () {
      this.searchKeyword = "";
    },

    viewUserDetail (user) {
      uni.navigateTo({
        url: `/pages/admin/users/info?userId=${user.id}`,
      });
    },

    async banUser (user) {
      uni.showModal({
        title: "确认禁用",
        content: `确定要禁用用户 ${user.username || user.nickname} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              // TODO: 调用API禁用用户
              // 这里应该调用实际的API来禁用用户
              // const response = await toggleUserStatus(user.id, 0);

              // 临时处理：从正常用户列表中移除，添加到禁用用户列表
              const index = this.users.findIndex(u => u.id === user.id);
              if (index > -1) {
                const bannedUser = { ...this.users.splice(index, 1)[0], status: 0 };
                this.bannedUsers.push(bannedUser);
                this.updateTabTitles();
              }

              uni.showToast({
                title: "禁用成功",
                icon: "success",
              });
            } catch (error) {
              console.error('禁用用户失败:', error);
              uni.showToast({
                title: "禁用失败",
                icon: "none",
              });
            }
          }
        },
      });
    },

    async unbanUser (user) {
      uni.showModal({
        title: "确认启用",
        content: `确定要启用用户 ${user.username || user.nickname} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              // TODO: 调用API启用用户
              // 这里应该调用实际的API来启用用户
              // const response = await toggleUserStatus(user.id, 1);

              // 临时处理：从禁用用户列表中移除，添加到正常用户列表
              const index = this.bannedUsers.findIndex(u => u.id === user.id);
              if (index > -1) {
                const normalUser = { ...this.bannedUsers.splice(index, 1)[0], status: 1 };
                this.users.push(normalUser);
                this.updateTabTitles();
              }

              uni.showToast({
                title: "启用成功",
                icon: "success",
              });
            } catch (error) {
              console.error('启用用户失败:', error);
              uni.showToast({
                title: "启用失败",
                icon: "none",
              });
            }
          }
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import '../../../styles/index.scss';

/* 页面特定样式 - 保留user-list页面的特殊布局 */
.container {
  background: #f5f5f5;
  position: relative;
}

/* 用户列表页面特有的固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #ffffff;
  border-bottom: 1rpx solid #e8eaec;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.header-content {
  padding: 12rpx 20rpx 16rpx;
}

/* 用户列表页面特有的列表容器样式 */
.list-container {
  padding: 8rpx 16rpx 16rpx;
  margin-top: 260rpx;
  // padding-top: 12rem;
  /* 为固定头部留出空间 */
}
</style>